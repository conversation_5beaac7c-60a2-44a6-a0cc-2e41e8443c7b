#!/bin/bash

# Cloudflare Pages Build Script for Poy8 Chess
echo "🚀 Starting Poy8 Chess build process..."

# Check Node.js version
echo "📋 Node.js version: $(node --version)"
echo "📋 NPM version: $(npm --version)"

# Clean install dependencies (fallback to regular install if ci fails)
echo "📦 Installing dependencies..."
if npm ci --no-audit --no-fund; then
    echo "✅ Dependencies installed with npm ci"
else
    echo "⚠️ npm ci failed, falling back to npm install"
    npm install --no-audit --no-fund
fi

# Build the project
echo "🔨 Building project..."
npm run build

echo "✅ Build completed successfully!"
echo "📁 Build output directory: dist/"
ls -la dist/
