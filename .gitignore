# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Build outputs
build
out

# Cache
.cache
.parcel-cache
.next
.nuxt
.vuepress/dist

# Temporary files
*.tmp
*.temp

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Package manager lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# PWA files
sw.js
workbox-*.js
