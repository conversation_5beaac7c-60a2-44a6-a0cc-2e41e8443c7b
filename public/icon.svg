<svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="80" fill="url(#bg)"/>
  
  <!-- Chess piece silhouette -->
  <g transform="translate(256,256)">
    <!-- <PERSON> crown -->
    <path d="M-60,-120 L-40,-140 L-20,-120 L0,-140 L20,-120 L40,-140 L60,-120 L60,-100 L-60,-100 Z" fill="white" opacity="0.9"/>
    
    <!-- <PERSON> body -->
    <rect x="-50" y="-100" width="100" height="80" rx="10" fill="white" opacity="0.9"/>
    
    <!-- Base -->
    <ellipse cx="0" cy="-10" rx="70" ry="15" fill="white" opacity="0.9"/>
    <rect x="-80" y="-10" width="160" height="30" fill="white" opacity="0.9"/>
    <ellipse cx="0" cy="20" rx="80" ry="20" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Text -->
  <text x="256" y="420" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">Poy8 Card</text>
</svg>
