这原本是一个棋类型游戏网站,现在我需要你改为一个消除类游戏网站叫Poy8 Eliminating game 免费消除小游戏,域名是eliminating.poy8.com

# 网站背景
这是一个消除类游戏网站的合集,网站首页直接展示8个消除游戏的消除,有封面图游戏名称和开始游戏按钮和游戏的简短介绍.点击开始按钮进入游戏后是一个H5网页游戏,最上面显示是游戏主体(占据整个窗口),滑动到下面,是游戏标签模块(给当前游戏增加标签分类),其他游戏推荐模块,游戏介绍文章模块(介绍当前游戏的历史背景,发明人,流行普及度,游戏操作方式,游戏玩法,游戏难点,游戏经验),游戏视频推荐模块(推荐热门youtube视频).
导航栏只有三个按钮,Poy8.com、Home页面、About页面.链接就直接跳转到poy8.com的主网站,主页就是上面说的消除游戏页面,about就是介绍当前网站的游戏页面.
首页的8个游戏,需要你帮我找8个最热门的消除游戏,构建h5游戏.需要兼容多种浏览器.

# 开发要求
我希望游戏使用vue3、vite、typescript、tailwindcss构建样式.我希望游戏偏向静态化,用户加载网站内容后,离线也能开始游戏,整个游戏在浏览器中缓存并加载,不占用服务端资源.网站需要包含导航栏和Footer.游戏以白色风格为主.tailwind需要使用成熟稳定版本,不要直接使用最新版.最后我会上传GitHub并部署到cloudflare page

# 网站多语言
网站需要支持多语言i18n,需要支持语言为:英文(网站默认语言)、日文、韩文、中文、法语、西班牙语、葡萄牙语、意大利语、德语、拉丁语.