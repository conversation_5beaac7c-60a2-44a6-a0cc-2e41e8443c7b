# Poy8 Card - Free Chess Games

A modern, multilingual chess games collection built with Vue 3, TypeScript, and Tailwind CSS. Play popular board games offline in your browser with PWA support.

## 🎮 Features

- **4 Classic Games**: Chess, Checkers, Reversi (Othello), and Gomoku (Five in a Row)
- **Offline Play**: Full PWA support with service worker caching
- **Multilingual**: Support for 10 languages (English, Chinese, Japanese, Korean, French, Spanish, Portuguese, Italian, German, Latin)
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **No Ads**: Completely free with no advertisements or subscriptions
- **Local Storage**: Game statistics and preferences saved locally

## 🚀 Quick Start

### Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open http://localhost:5173
```

### Production

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 🛠️ Tech Stack

- **Frontend**: Vue 3 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **Internationalization**: Vue I18n
- **State Management**: Pinia
- **PWA**: Vite PWA Plugin with Workbox
- **Routing**: Vue Router

## 🎯 Game Features

### Chess
- Full chess implementation with piece movement validation
- Visual move highlighting and last move indication
- Undo functionality and move history
- Responsive board with coordinate labels

### Gomoku (Five in a Row)
- 15x15 board with traditional Go board styling
- Win detection for 5 consecutive pieces
- Visual winning line highlight
- Undo moves and game reset

### Coming Soon
- Checkers/Draughts
- Reversi/Othello

## 🌍 Supported Languages

- 🇺🇸 English (Default)
- 🇨🇳 中文 (Chinese)
- 🇯🇵 日本語 (Japanese)
- 🇰🇷 한국어 (Korean)
- 🇫🇷 Français (French)
- 🇪🇸 Español (Spanish)
- 🇵🇹 Português (Portuguese)
- 🇮🇹 Italiano (Italian)
- 🇩🇪 Deutsch (German)
- 🏛️ Latina (Latin)

## 📱 PWA Features

- **Offline Play**: All games work without internet connection
- **App-like Experience**: Install on mobile devices and desktop
- **Fast Loading**: Cached resources for instant startup
- **Background Updates**: Automatic updates when online

## 🎨 Design

- **Clean Interface**: Minimalist white theme with blue accents
- **Intuitive Navigation**: Easy-to-use navigation with language selector
- **Game-focused**: Distraction-free gaming experience
- **Accessibility**: Keyboard navigation and screen reader support

## 📂 Project Structure

```
src/
├── components/          # Reusable Vue components
├── games/              # Game implementations
│   ├── chess/          # Chess game
│   └── gomoku/         # Gomoku game
├── locales/            # Internationalization files
├── router/             # Vue Router configuration
├── stores/             # Pinia stores
├── views/              # Page components
└── assets/             # Static assets and styles
```

## 🔧 Configuration

### Environment Variables

No environment variables required for basic functionality.

### PWA Configuration

PWA settings can be modified in `vite.config.ts`:

```typescript
VitePWA({
  registerType: 'autoUpdate',
  workbox: {
    globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}']
  },
  manifest: {
    name: 'Poy8 Card - Free Chess Games',
    short_name: 'Poy8 Chess',
    // ... other manifest options
  }
})
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Chess piece Unicode symbols
- Vue.js ecosystem and community
- Tailwind CSS for styling utilities
- All contributors and testers

## 📞 Support

For support, please visit [Poy8.com](https://poy8.com) or open an issue on GitHub.

---

**Enjoy playing! 🎮**
