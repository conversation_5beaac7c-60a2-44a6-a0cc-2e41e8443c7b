<template>
  <div class="mahjong-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.mahjong.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ tilesRemaining }}</div>
          <div class="text-sm text-gray-500">Tiles Left</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ matches }}</div>
          <div class="text-sm text-gray-500">Matches</div>
        </div>
      </div>
      <div class="mt-2">
        <div class="text-sm text-gray-600">
          Time: {{ formatTime(timeElapsed) }}
        </div>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="mahjong-board mx-auto" :style="{ width: boardWidth + 'px', height: boardHeight + 'px' }">
      <div
        v-for="(tile, index) in tiles"
        :key="tile.id"
        class="mahjong-tile"
        :class="{
          'selected': selectedTiles.includes(index),
          'matched': tile.matched,
          'available': isTileAvailable(index),
          'hint': hintTiles.includes(index)
        }"
        :style="{
          left: tile.x + 'px',
          top: tile.y + 'px',
          zIndex: tile.layer * 10 + 1
        }"
        @click="selectTile(index)"
      >
        <div class="tile-face" :style="{ backgroundColor: getTileColor(tile.type) }">
          <div class="tile-symbol">{{ getTileSymbol(tile.type) }}</div>
          <div class="tile-border"></div>
        </div>
        <div class="tile-shadow"></div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="shuffleTiles" class="btn btn-primary" :disabled="!canShuffle">
        Shuffle
      </button>
      <button @click="getHint" class="btn btn-primary" :disabled="!canGetHint">
        Hint
      </button>
      <button @click="undoMove" class="btn btn-primary" :disabled="moveHistory.length === 0">
        Undo
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Types
interface Tile {
  id: number
  type: number
  x: number
  y: number
  layer: number
  matched: boolean
}

interface GameResult {
  title: string
  message: string
}

interface Move {
  tile1Index: number
  tile2Index: number
}

// Constants
const TILE_TYPES = 12
const TILE_WIDTH = 40
const TILE_HEIGHT = 50
const LAYER_OFFSET = 3
const COLORS = [
  '#ef4444', '#3b82f6', '#10b981', '#f59e0b', 
  '#8b5cf6', '#f97316', '#ec4899', '#06b6d4',
  '#84cc16', '#f43f5e', '#6366f1', '#14b8a6'
]
const SYMBOLS = [
  '🀄', '🀅', '🀆', '🀇', '🀈', '🀉', 
  '🀊', '🀋', '🀌', '🀍', '🀎', '🀏'
]

// Layout pattern for classic Mahjong solitaire
const LAYOUT_PATTERN = [
  // Layer 0 (bottom)
  [
    '    XXX    ',
    '  XXXXXXX  ',
    ' XXXXXXXXX ',
    'XXXXXXXXXXX',
    ' XXXXXXXXX ',
    '  XXXXXXX  ',
    '    XXX    '
  ],
  // Layer 1
  [
    '           ',
    '   XXXXX   ',
    '  XXXXXXX  ',
    '   XXXXX   ',
    '  XXXXXXX  ',
    '   XXXXX   ',
    '           '
  ],
  // Layer 2
  [
    '           ',
    '           ',
    '    XXX    ',
    '    XXX    ',
    '    XXX    ',
    '           ',
    '           '
  ],
  // Layer 3 (top)
  [
    '           ',
    '           ',
    '           ',
    '     X     ',
    '           ',
    '           ',
    '           '
  ]
]

// Reactive state
const tiles = ref<Tile[]>([])
const selectedTiles = ref<number[]>([])
const hintTiles = ref<number[]>([])
const score = ref(0)
const matches = ref(0)
const timeElapsed = ref(0)
const gameResult = ref<GameResult | null>(null)
const boardWidth = ref(500)
const boardHeight = ref(400)
const canShuffle = ref(true)
const canGetHint = ref(true)
const moveHistory = ref<Move[]>([])
const gameTimer = ref<number | null>(null)
const tileIdCounter = ref(0)

// Computed
const tilesRemaining = computed(() => {
  return tiles.value.filter(tile => !tile.matched).length
})

const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (selectedTiles.value.length === 1) return 'Select a matching tile'
  return 'Select two matching tiles to remove them'
})

// Initialize game
const initializeGame = () => {
  tiles.value = []
  selectedTiles.value = []
  hintTiles.value = []
  score.value = 0
  matches.value = 0
  timeElapsed.value = 0
  gameResult.value = null
  canShuffle.value = true
  canGetHint.value = true
  moveHistory.value = []
  tileIdCounter.value = 0
  
  generateTiles()
  startTimer()
  updateBoardSize()
}

// Generate tiles based on layout pattern
const generateTiles = () => {
  const tileTypes: number[] = []
  
  // Count total tiles needed
  let totalTiles = 0
  for (const layer of LAYOUT_PATTERN) {
    for (const row of layer) {
      totalTiles += row.split('').filter(char => char === 'X').length
    }
  }
  
  // Create pairs of tiles (each type appears twice)
  const tilesPerType = Math.floor(totalTiles / TILE_TYPES / 2) * 2
  for (let type = 0; type < TILE_TYPES; type++) {
    for (let i = 0; i < tilesPerType; i++) {
      tileTypes.push(type)
    }
  }
  
  // Fill remaining slots
  while (tileTypes.length < totalTiles) {
    tileTypes.push(Math.floor(Math.random() * TILE_TYPES))
  }
  
  // Shuffle tile types
  for (let i = tileTypes.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[tileTypes[i], tileTypes[j]] = [tileTypes[j], tileTypes[i]]
  }
  
  // Place tiles according to layout pattern
  let tileIndex = 0
  for (let layer = 0; layer < LAYOUT_PATTERN.length; layer++) {
    const pattern = LAYOUT_PATTERN[layer]
    for (let row = 0; row < pattern.length; row++) {
      for (let col = 0; col < pattern[row].length; col++) {
        if (pattern[row][col] === 'X') {
          tiles.value.push({
            id: tileIdCounter.value++,
            type: tileTypes[tileIndex++],
            x: col * (TILE_WIDTH - 5) + 20,
            y: row * (TILE_HEIGHT - 10) + 20 - layer * LAYER_OFFSET,
            layer,
            matched: false
          })
        }
      }
    }
  }
}

// Check if tile is available (can be selected)
const isTileAvailable = (index: number): boolean => {
  const tile = tiles.value[index]
  if (tile.matched) return false
  
  // Check if tile is blocked by tiles on higher layers
  for (const otherTile of tiles.value) {
    if (otherTile.matched || otherTile.layer <= tile.layer) continue
    
    // Check if other tile overlaps with this tile
    if (Math.abs(otherTile.x - tile.x) < TILE_WIDTH - 5 && 
        Math.abs(otherTile.y - tile.y) < TILE_HEIGHT - 5) {
      return false
    }
  }
  
  // Check if tile has free sides (left or right)
  let leftFree = true
  let rightFree = true
  
  for (const otherTile of tiles.value) {
    if (otherTile.matched || otherTile.layer !== tile.layer || otherTile === tile) continue
    
    // Check left side
    if (Math.abs(otherTile.y - tile.y) < TILE_HEIGHT - 10 && 
        otherTile.x === tile.x - (TILE_WIDTH - 5)) {
      leftFree = false
    }
    
    // Check right side
    if (Math.abs(otherTile.y - tile.y) < TILE_HEIGHT - 10 && 
        otherTile.x === tile.x + (TILE_WIDTH - 5)) {
      rightFree = false
    }
  }
  
  return leftFree || rightFree
}

// Select tile
const selectTile = (index: number) => {
  if (gameResult.value || !isTileAvailable(index)) return
  
  const tile = tiles.value[index]
  
  if (selectedTiles.value.includes(index)) {
    // Deselect tile
    selectedTiles.value = selectedTiles.value.filter(i => i !== index)
    hintTiles.value = []
  } else if (selectedTiles.value.length === 0) {
    // First tile selection
    selectedTiles.value = [index]
    hintTiles.value = []
  } else if (selectedTiles.value.length === 1) {
    const firstIndex = selectedTiles.value[0]
    const firstTile = tiles.value[firstIndex]
    
    if (firstTile.type === tile.type) {
      // Matching tiles - remove them
      matchTiles(firstIndex, index)
    } else {
      // Different tiles - select new one
      selectedTiles.value = [index]
    }
  }
}

// Match tiles
const matchTiles = (index1: number, index2: number) => {
  tiles.value[index1].matched = true
  tiles.value[index2].matched = true
  
  // Record move for undo
  moveHistory.value.push({ tile1Index: index1, tile2Index: index2 })
  
  selectedTiles.value = []
  hintTiles.value = []
  matches.value++
  score.value += 10
  
  // Check win condition
  if (tilesRemaining.value === 0) {
    gameWon()
  } else if (!hasAvailableMoves()) {
    gameOver()
  }
}

// Check if there are available moves
const hasAvailableMoves = (): boolean => {
  const availableTiles = tiles.value
    .map((tile, index) => ({ tile, index }))
    .filter(({ tile, index }) => !tile.matched && isTileAvailable(index))
  
  for (let i = 0; i < availableTiles.length; i++) {
    for (let j = i + 1; j < availableTiles.length; j++) {
      if (availableTiles[i].tile.type === availableTiles[j].tile.type) {
        return true
      }
    }
  }
  
  return false
}

// Get hint
const getHint = () => {
  if (!canGetHint.value) return
  
  const availableTiles = tiles.value
    .map((tile, index) => ({ tile, index }))
    .filter(({ tile, index }) => !tile.matched && isTileAvailable(index))
  
  for (let i = 0; i < availableTiles.length; i++) {
    for (let j = i + 1; j < availableTiles.length; j++) {
      if (availableTiles[i].tile.type === availableTiles[j].tile.type) {
        hintTiles.value = [availableTiles[i].index, availableTiles[j].index]
        
        setTimeout(() => {
          hintTiles.value = []
        }, 3000)
        
        canGetHint.value = false
        setTimeout(() => {
          canGetHint.value = true
        }, 10000)
        
        return
      }
    }
  }
}

// Shuffle tiles
const shuffleTiles = () => {
  if (!canShuffle.value) return
  
  const unmatchedTiles = tiles.value.filter(tile => !tile.matched)
  const types = unmatchedTiles.map(tile => tile.type)
  
  // Shuffle types
  for (let i = types.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[types[i], types[j]] = [types[j], types[i]]
  }
  
  // Reassign types
  let typeIndex = 0
  for (const tile of tiles.value) {
    if (!tile.matched) {
      tile.type = types[typeIndex++]
    }
  }
  
  selectedTiles.value = []
  hintTiles.value = []
  canShuffle.value = false
  
  // Re-enable shuffle after delay
  setTimeout(() => {
    canShuffle.value = true
  }, 30000)
}

// Undo move
const undoMove = () => {
  if (moveHistory.value.length === 0) return
  
  const lastMove = moveHistory.value.pop()!
  tiles.value[lastMove.tile1Index].matched = false
  tiles.value[lastMove.tile2Index].matched = false
  
  selectedTiles.value = []
  hintTiles.value = []
  matches.value--
  score.value -= 10
}

// Get tile color
const getTileColor = (type: number): string => {
  return COLORS[type] || '#gray'
}

// Get tile symbol
const getTileSymbol = (type: number): string => {
  return SYMBOLS[type] || '🀫'
}

// Format time
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Start timer
const startTimer = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
  }
  
  gameTimer.value = setInterval(() => {
    timeElapsed.value++
  }, 1000)
}

// Game won
const gameWon = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
    gameTimer.value = null
  }
  
  const timeBonus = Math.max(0, 300 - timeElapsed.value) * 2
  score.value += timeBonus
  
  gameResult.value = {
    title: 'Congratulations!',
    message: `You won! Score: ${score.value} | Time: ${formatTime(timeElapsed.value)}`
  }
}

// Game over
const gameOver = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
    gameTimer.value = null
  }
  
  gameResult.value = {
    title: 'Game Over!',
    message: `No more moves available. Score: ${score.value} | Time: ${formatTime(timeElapsed.value)}`
  }
}

// Reset game
const resetGame = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
    gameTimer.value = null
  }
  initializeGame()
}

// Update board size
const updateBoardSize = () => {
  const maxWidth = Math.min(window.innerWidth - 40, 600)
  boardWidth.value = Math.max(400, maxWidth)
  boardHeight.value = boardWidth.value * 0.8
}

onMounted(() => {
  initializeGame()
  window.addEventListener('resize', updateBoardSize)
})

onUnmounted(() => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
  }
  window.removeEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.mahjong-board {
  position: relative;
  background: linear-gradient(135deg, #065f46, #047857);
  border: 3px solid #059669;
  border-radius: 12px;
  overflow: visible;
  margin: 0 auto;
}

.mahjong-tile {
  position: absolute;
  width: 40px;
  height: 50px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mahjong-tile:not(.available) {
  cursor: not-allowed;
  opacity: 0.6;
}

.mahjong-tile.available:hover {
  transform: translateY(-2px);
}

.mahjong-tile.selected {
  transform: translateY(-4px) scale(1.05);
}

.mahjong-tile.matched {
  opacity: 0;
  pointer-events: none;
  transform: scale(0);
}

.mahjong-tile.hint {
  animation: hintPulse 1s infinite;
}

.tile-face {
  width: 100%;
  height: 100%;
  border: 2px solid #ffffff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 
    inset 0 2px 4px rgba(255,255,255,0.3),
    0 2px 8px rgba(0,0,0,0.2);
}

.tile-symbol {
  font-size: 1.2em;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  z-index: 2;
}

.tile-border {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 4px;
  pointer-events: none;
}

.tile-shadow {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  border-radius: 6px;
  z-index: -1;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

@keyframes hintPulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% { 
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}
</style>
