<template>
  <div class="match3-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.match3.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ moves }}</div>
          <div class="text-sm text-gray-500">Moves</div>
        </div>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="match3-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <div 
        v-for="(row, rowIndex) in board" 
        :key="rowIndex"
        class="match3-row"
      >
        <div
          v-for="(gem, colIndex) in row"
          :key="colIndex"
          class="match3-cell"
          :class="{
            'selected': selectedGem && selectedGem.row === rowIndex && selectedGem.col === colIndex,
            'highlighted': isHighlighted(rowIndex, colIndex),
            'falling': gem && gem.falling
          }"
          @click="handleGemClick(rowIndex, colIndex)"
        >
          <div v-if="gem" class="gem" :class="gem.type" :style="{ backgroundColor: getGemColor(gem.type) }">
            <div class="gem-inner">{{ getGemSymbol(gem.type) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="shuffleBoard" class="btn btn-primary" :disabled="!canShuffle">
        Shuffle
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'

// Types
interface Gem {
  type: number
  falling?: boolean
  matched?: boolean
}

interface Position {
  row: number
  col: number
}

interface GameResult {
  title: string
  message: string
}

// Constants
const BOARD_SIZE = 8
const GEM_TYPES = 6
const COLORS = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']
const SYMBOLS = ['💎', '🔷', '🔶', '💠', '🔸', '🔹']

// Reactive state
const board = ref<(Gem | null)[][]>([])
const selectedGem = ref<Position | null>(null)
const score = ref(0)
const level = ref(1)
const moves = ref(30)
const gameResult = ref<GameResult | null>(null)
const boardSize = ref(400)
const highlightedCells = ref<Position[]>([])
const canShuffle = ref(true)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (moves.value <= 0) return 'No moves left!'
  return `Match 3 or more gems to score points!`
})

// Initialize board
const initializeBoard = () => {
  board.value = Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(null))
  
  // Fill board with random gems, ensuring no initial matches
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE; col++) {
      let gemType: number
      do {
        gemType = Math.floor(Math.random() * GEM_TYPES)
      } while (wouldCreateMatch(row, col, gemType))
      
      board.value[row][col] = { type: gemType }
    }
  }
  
  selectedGem.value = null
  score.value = 0
  level.value = 1
  moves.value = 30
  gameResult.value = null
  highlightedCells.value = []
  canShuffle.value = true
}

// Check if placing a gem would create a match
const wouldCreateMatch = (row: number, col: number, gemType: number): boolean => {
  // Check horizontal
  let horizontalCount = 1
  // Check left
  for (let c = col - 1; c >= 0 && board.value[row][c]?.type === gemType; c--) {
    horizontalCount++
  }
  // Check right
  for (let c = col + 1; c < BOARD_SIZE && board.value[row][c]?.type === gemType; c++) {
    horizontalCount++
  }
  
  // Check vertical
  let verticalCount = 1
  // Check up
  for (let r = row - 1; r >= 0 && board.value[r][col]?.type === gemType; r--) {
    verticalCount++
  }
  // Check down
  for (let r = row + 1; r < BOARD_SIZE && board.value[r][col]?.type === gemType; r++) {
    verticalCount++
  }
  
  return horizontalCount >= 3 || verticalCount >= 3
}

// Handle gem click
const handleGemClick = (row: number, col: number) => {
  if (gameResult.value || moves.value <= 0) return
  
  const clickedPos = { row, col }
  
  if (!selectedGem.value) {
    // First gem selection
    selectedGem.value = clickedPos
    highlightPossibleMoves(row, col)
  } else if (selectedGem.value.row === row && selectedGem.value.col === col) {
    // Deselect
    selectedGem.value = null
    highlightedCells.value = []
  } else if (isAdjacent(selectedGem.value, clickedPos)) {
    // Try to swap
    swapGems(selectedGem.value, clickedPos)
  } else {
    // Select new gem
    selectedGem.value = clickedPos
    highlightPossibleMoves(row, col)
  }
}

// Check if two positions are adjacent
const isAdjacent = (pos1: Position, pos2: Position): boolean => {
  const rowDiff = Math.abs(pos1.row - pos2.row)
  const colDiff = Math.abs(pos1.col - pos2.col)
  return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)
}

// Highlight possible moves
const highlightPossibleMoves = (row: number, col: number) => {
  highlightedCells.value = []
  const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
  
  for (const [dr, dc] of directions) {
    const newRow = row + dr
    const newCol = col + dc
    if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE) {
      highlightedCells.value.push({ row: newRow, col: newCol })
    }
  }
}

// Check if cell is highlighted
const isHighlighted = (row: number, col: number): boolean => {
  return highlightedCells.value.some(pos => pos.row === row && pos.col === col)
}

// Swap gems
const swapGems = async (pos1: Position, pos2: Position) => {
  const gem1 = board.value[pos1.row][pos1.col]
  const gem2 = board.value[pos2.row][pos2.col]
  
  // Perform swap
  board.value[pos1.row][pos1.col] = gem2
  board.value[pos2.row][pos2.col] = gem1
  
  // Check for matches
  const matches = findMatches()
  
  if (matches.length > 0) {
    // Valid move
    moves.value--
    selectedGem.value = null
    highlightedCells.value = []
    
    await processMatches()
    
    // Check for game over
    if (moves.value <= 0) {
      gameResult.value = {
        title: 'Game Over!',
        message: `Final Score: ${score.value}`
      }
    }
  } else {
    // Invalid move, swap back
    board.value[pos1.row][pos1.col] = gem1
    board.value[pos2.row][pos2.col] = gem2
  }
}

// Find matches on the board
const findMatches = (): Position[] => {
  const matches: Position[] = []
  const visited = Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(false))
  
  // Check horizontal matches
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE - 2; col++) {
      const gem = board.value[row][col]
      if (!gem) continue
      
      let count = 1
      let endCol = col
      
      for (let c = col + 1; c < BOARD_SIZE && board.value[row][c]?.type === gem.type; c++) {
        count++
        endCol = c
      }
      
      if (count >= 3) {
        for (let c = col; c <= endCol; c++) {
          if (!visited[row][c]) {
            matches.push({ row, col: c })
            visited[row][c] = true
          }
        }
      }
    }
  }
  
  // Check vertical matches
  for (let col = 0; col < BOARD_SIZE; col++) {
    for (let row = 0; row < BOARD_SIZE - 2; row++) {
      const gem = board.value[row][col]
      if (!gem) continue
      
      let count = 1
      let endRow = row
      
      for (let r = row + 1; r < BOARD_SIZE && board.value[r][col]?.type === gem.type; r++) {
        count++
        endRow = r
      }
      
      if (count >= 3) {
        for (let r = row; r <= endRow; r++) {
          if (!visited[r][col]) {
            matches.push({ row: r, col })
            visited[r][col] = true
          }
        }
      }
    }
  }
  
  return matches
}

// Process matches and update score
const processMatches = async () => {
  let totalMatches = 0
  
  while (true) {
    const matches = findMatches()
    if (matches.length === 0) break
    
    // Remove matched gems
    for (const match of matches) {
      board.value[match.row][match.col] = null
    }
    
    totalMatches += matches.length
    
    // Apply gravity
    await applyGravity()
    
    // Fill empty spaces
    fillEmptySpaces()
    
    await nextTick()
  }
  
  // Update score
  if (totalMatches > 0) {
    const baseScore = totalMatches * 10
    const levelBonus = level.value * 5
    score.value += baseScore + levelBonus
    
    // Level up every 1000 points
    const newLevel = Math.floor(score.value / 1000) + 1
    if (newLevel > level.value) {
      level.value = newLevel
      moves.value += 5 // Bonus moves for leveling up
    }
  }
}

// Apply gravity to make gems fall
const applyGravity = async () => {
  for (let col = 0; col < BOARD_SIZE; col++) {
    const column = []
    
    // Collect non-null gems
    for (let row = BOARD_SIZE - 1; row >= 0; row--) {
      if (board.value[row][col]) {
        column.push(board.value[row][col])
      }
    }
    
    // Clear column
    for (let row = 0; row < BOARD_SIZE; row++) {
      board.value[row][col] = null
    }
    
    // Place gems at bottom
    for (let i = 0; i < column.length; i++) {
      board.value[BOARD_SIZE - 1 - i][col] = column[i]
    }
  }
}

// Fill empty spaces with new gems
const fillEmptySpaces = () => {
  for (let col = 0; col < BOARD_SIZE; col++) {
    for (let row = 0; row < BOARD_SIZE; row++) {
      if (!board.value[row][col]) {
        board.value[row][col] = {
          type: Math.floor(Math.random() * GEM_TYPES),
          falling: true
        }
      }
    }
  }
  
  // Remove falling flag after animation
  setTimeout(() => {
    for (let row = 0; row < BOARD_SIZE; row++) {
      for (let col = 0; col < BOARD_SIZE; col++) {
        if (board.value[row][col]) {
          board.value[row][col]!.falling = false
        }
      }
    }
  }, 300)
}

// Shuffle board
const shuffleBoard = () => {
  if (!canShuffle.value) return
  
  const gems: Gem[] = []
  
  // Collect all gems
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE; col++) {
      if (board.value[row][col]) {
        gems.push(board.value[row][col]!)
      }
    }
  }
  
  // Shuffle gems
  for (let i = gems.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[gems[i], gems[j]] = [gems[j], gems[i]]
  }
  
  // Place shuffled gems back
  let gemIndex = 0
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE; col++) {
      board.value[row][col] = gems[gemIndex++] || null
    }
  }
  
  selectedGem.value = null
  highlightedCells.value = []
  canShuffle.value = false
  
  // Re-enable shuffle after some moves
  setTimeout(() => {
    canShuffle.value = true
  }, 5000)
}

// Get gem color
const getGemColor = (type: number): string => {
  return COLORS[type] || '#gray'
}

// Get gem symbol
const getGemSymbol = (type: number): string => {
  return SYMBOLS[type] || '💎'
}

// Reset game
const resetGame = () => {
  initializeBoard()
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 500)
  boardSize.value = Math.max(300, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.match3-board {
  display: grid;
  grid-template-rows: repeat(8, 1fr);
  gap: 2px;
  background-color: #f3f4f6;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  padding: 8px;
}

.match3-row {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 2px;
}

.match3-cell {
  aspect-ratio: 1;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.match3-cell:hover {
  background-color: #f9fafb;
  transform: scale(1.05);
}

.match3-cell.selected {
  background-color: #dbeafe;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px #3b82f6;
}

.match3-cell.highlighted {
  background-color: #fef3c7;
  border-color: #f59e0b;
}

.gem {
  width: 90%;
  height: 90%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.gem.falling {
  animation: fall 0.3s ease-out;
}

.gem-inner {
  font-size: 1.2em;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

@keyframes fall {
  from {
    transform: translateY(-100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
