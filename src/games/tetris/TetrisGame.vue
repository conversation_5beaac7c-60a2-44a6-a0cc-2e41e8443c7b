<template>
  <div class="tetris-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.tetris.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ lines }}</div>
          <div class="text-sm text-gray-500">Lines</div>
        </div>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Layout -->
    <div class="game-layout">
      <!-- Next Piece Preview -->
      <div class="next-piece-container">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Next</h3>
        <div class="next-piece-preview">
          <div
            v-for="(row, rowIndex) in nextPieceGrid"
            :key="rowIndex"
            class="preview-row"
          >
            <div
              v-for="(cell, colIndex) in row"
              :key="colIndex"
              class="preview-cell"
              :class="{ filled: cell }"
              :style="{ backgroundColor: cell ? getPieceColor(nextPiece?.type || 0) : 'transparent' }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Game Board -->
      <div class="tetris-board" :style="{ width: boardWidth + 'px', height: boardHeight + 'px' }">
        <div
          v-for="(row, rowIndex) in displayBoard"
          :key="rowIndex"
          class="tetris-row"
        >
          <div
            v-for="(cell, colIndex) in row"
            :key="colIndex"
            class="tetris-cell"
            :class="{ filled: cell > 0, ghost: cell === -1 }"
            :style="{ backgroundColor: cell > 0 ? getPieceColor(cell) : 'transparent' }"
          ></div>
        </div>
      </div>

      <!-- Hold Piece -->
      <div class="hold-piece-container">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Hold</h3>
        <div class="hold-piece-preview">
          <div
            v-for="(row, rowIndex) in holdPieceGrid"
            :key="rowIndex"
            class="preview-row"
          >
            <div
              v-for="(cell, colIndex) in row"
              :key="colIndex"
              class="preview-cell"
              :class="{ filled: cell }"
              :style="{ backgroundColor: cell ? getPieceColor(holdPiece?.type || 0) : 'transparent' }"
            ></div>
          </div>
        </div>
        <button @click="holdCurrentPiece" class="btn btn-sm btn-secondary mt-2" :disabled="!canHold">
          Hold (C)
        </button>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="pauseGame" class="btn btn-primary" v-if="!gameResult">
        {{ isPaused ? 'Resume' : 'Pause' }}
      </button>
      <button @click="dropPiece" class="btn btn-primary" v-if="!gameResult && !isPaused">
        Drop (Space)
      </button>
    </div>

    <!-- Controls Help -->
    <div class="mt-4 text-center text-sm text-gray-600">
      <p>Use ← → to move, ↓ to soft drop, ↑ to rotate, Space to hard drop, C to hold</p>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// Types
interface Piece {
  type: number
  shape: number[][]
  x: number
  y: number
}

interface GameResult {
  title: string
  message: string
}

// Constants
const BOARD_WIDTH = 10
const BOARD_HEIGHT = 20
const CELL_SIZE = 25

// Tetris pieces (I, O, T, S, Z, J, L)
const PIECES = [
  { type: 1, shape: [[1,1,1,1]] }, // I
  { type: 2, shape: [[1,1],[1,1]] }, // O
  { type: 3, shape: [[0,1,0],[1,1,1]] }, // T
  { type: 4, shape: [[0,1,1],[1,1,0]] }, // S
  { type: 5, shape: [[1,1,0],[0,1,1]] }, // Z
  { type: 6, shape: [[1,0,0],[1,1,1]] }, // J
  { type: 7, shape: [[0,0,1],[1,1,1]] }  // L
]

const COLORS = [
  '#000000', // 0 - empty
  '#00f0f0', // 1 - I (cyan)
  '#f0f000', // 2 - O (yellow)
  '#a000f0', // 3 - T (purple)
  '#00f000', // 4 - S (green)
  '#f00000', // 5 - Z (red)
  '#0000f0', // 6 - J (blue)
  '#f0a000'  // 7 - L (orange)
]

// Reactive state
const board = ref<number[][]>([])
const currentPiece = ref<Piece | null>(null)
const nextPiece = ref<Piece | null>(null)
const holdPiece = ref<Piece | null>(null)
const score = ref(0)
const level = ref(1)
const lines = ref(0)
const gameResult = ref<GameResult | null>(null)
const isPaused = ref(false)
const canHold = ref(true)
const dropTimer = ref<number | null>(null)
const boardWidth = ref(BOARD_WIDTH * CELL_SIZE)
const boardHeight = ref(BOARD_HEIGHT * CELL_SIZE)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (isPaused.value) return 'Game Paused'
  return 'Use arrow keys to move and rotate pieces!'
})

const displayBoard = computed(() => {
  const display = board.value.map(row => [...row])
  
  // Add current piece to display
  if (currentPiece.value && !isPaused.value) {
    const piece = currentPiece.value
    
    // Add ghost piece
    const ghostY = getGhostPosition()
    addPieceToBoard(display, piece, piece.x, ghostY, -1)
    
    // Add current piece
    addPieceToBoard(display, piece, piece.x, piece.y, piece.type)
  }
  
  return display
})

const nextPieceGrid = computed(() => {
  const grid = Array(4).fill(null).map(() => Array(4).fill(false))
  if (nextPiece.value) {
    const shape = nextPiece.value.shape
    for (let row = 0; row < shape.length; row++) {
      for (let col = 0; col < shape[row].length; col++) {
        if (shape[row][col]) {
          grid[row][col] = true
        }
      }
    }
  }
  return grid
})

const holdPieceGrid = computed(() => {
  const grid = Array(4).fill(null).map(() => Array(4).fill(false))
  if (holdPiece.value) {
    const shape = holdPiece.value.shape
    for (let row = 0; row < shape.length; row++) {
      for (let col = 0; col < shape[row].length; col++) {
        if (shape[row][col]) {
          grid[row][col] = true
        }
      }
    }
  }
  return grid
})

// Initialize game
const initializeGame = () => {
  // Initialize empty board
  board.value = Array(BOARD_HEIGHT).fill(null).map(() => Array(BOARD_WIDTH).fill(0))
  
  score.value = 0
  level.value = 1
  lines.value = 0
  gameResult.value = null
  isPaused.value = false
  canHold.value = true
  holdPiece.value = null
  
  // Create first pieces
  nextPiece.value = createRandomPiece()
  spawnNewPiece()
  
  startDropTimer()
}

// Create random piece
const createRandomPiece = (): Piece => {
  const pieceTemplate = PIECES[Math.floor(Math.random() * PIECES.length)]
  return {
    type: pieceTemplate.type,
    shape: pieceTemplate.shape.map(row => [...row]),
    x: Math.floor(BOARD_WIDTH / 2) - Math.floor(pieceTemplate.shape[0].length / 2),
    y: 0
  }
}

// Spawn new piece
const spawnNewPiece = () => {
  currentPiece.value = nextPiece.value
  nextPiece.value = createRandomPiece()
  canHold.value = true
  
  // Check if game over
  if (currentPiece.value && !isValidPosition(currentPiece.value, currentPiece.value.x, currentPiece.value.y)) {
    gameOver()
  }
}

// Check if position is valid
const isValidPosition = (piece: Piece, x: number, y: number): boolean => {
  for (let row = 0; row < piece.shape.length; row++) {
    for (let col = 0; col < piece.shape[row].length; col++) {
      if (piece.shape[row][col]) {
        const boardX = x + col
        const boardY = y + row
        
        if (boardX < 0 || boardX >= BOARD_WIDTH || 
            boardY >= BOARD_HEIGHT || 
            (boardY >= 0 && board.value[boardY][boardX] > 0)) {
          return false
        }
      }
    }
  }
  return true
}

// Add piece to board for display
const addPieceToBoard = (board: number[][], piece: Piece, x: number, y: number, value: number) => {
  for (let row = 0; row < piece.shape.length; row++) {
    for (let col = 0; col < piece.shape[row].length; col++) {
      if (piece.shape[row][col]) {
        const boardX = x + col
        const boardY = y + row
        
        if (boardX >= 0 && boardX < BOARD_WIDTH && boardY >= 0 && boardY < BOARD_HEIGHT) {
          board[boardY][boardX] = value
        }
      }
    }
  }
}

// Get ghost position
const getGhostPosition = (): number => {
  if (!currentPiece.value) return 0
  
  let ghostY = currentPiece.value.y
  while (isValidPosition(currentPiece.value, currentPiece.value.x, ghostY + 1)) {
    ghostY++
  }
  return ghostY
}

// Move piece
const movePiece = (dx: number, dy: number) => {
  if (!currentPiece.value || isPaused.value || gameResult.value) return
  
  const newX = currentPiece.value.x + dx
  const newY = currentPiece.value.y + dy
  
  if (isValidPosition(currentPiece.value, newX, newY)) {
    currentPiece.value.x = newX
    currentPiece.value.y = newY
    return true
  }
  return false
}

// Rotate piece
const rotatePiece = () => {
  if (!currentPiece.value || isPaused.value || gameResult.value) return
  
  const rotated = {
    ...currentPiece.value,
    shape: rotateMatrix(currentPiece.value.shape)
  }
  
  // Try rotation with wall kicks
  const kicks = [
    [0, 0], [-1, 0], [1, 0], [0, -1], [-1, -1], [1, -1]
  ]
  
  for (const [dx, dy] of kicks) {
    if (isValidPosition(rotated, rotated.x + dx, rotated.y + dy)) {
      currentPiece.value.shape = rotated.shape
      currentPiece.value.x += dx
      currentPiece.value.y += dy
      return
    }
  }
}

// Rotate matrix 90 degrees clockwise
const rotateMatrix = (matrix: number[][]): number[][] => {
  const rows = matrix.length
  const cols = matrix[0].length
  const rotated = Array(cols).fill(null).map(() => Array(rows).fill(0))
  
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      rotated[col][rows - 1 - row] = matrix[row][col]
    }
  }
  
  return rotated
}

// Drop piece one row
const dropPieceOneRow = () => {
  if (!movePiece(0, 1)) {
    lockPiece()
  }
}

// Hard drop piece
const dropPiece = () => {
  if (!currentPiece.value || isPaused.value || gameResult.value) return
  
  while (movePiece(0, 1)) {
    score.value += 2 // Bonus for hard drop
  }
  lockPiece()
}

// Lock piece in place
const lockPiece = () => {
  if (!currentPiece.value) return
  
  // Add piece to board
  addPieceToBoard(board.value, currentPiece.value, currentPiece.value.x, currentPiece.value.y, currentPiece.value.type)
  
  // Check for completed lines
  const completedLines = findCompletedLines()
  if (completedLines.length > 0) {
    clearLines(completedLines)
    updateScore(completedLines.length)
  }
  
  // Spawn next piece
  spawnNewPiece()
}

// Find completed lines
const findCompletedLines = (): number[] => {
  const completed: number[] = []
  
  for (let row = 0; row < BOARD_HEIGHT; row++) {
    if (board.value[row].every(cell => cell > 0)) {
      completed.push(row)
    }
  }
  
  return completed
}

// Clear completed lines
const clearLines = (lineNumbers: number[]) => {
  // Remove completed lines
  for (const lineNum of lineNumbers.sort((a, b) => b - a)) {
    board.value.splice(lineNum, 1)
    board.value.unshift(Array(BOARD_WIDTH).fill(0))
  }
}

// Update score and level
const updateScore = (linesCleared: number) => {
  const lineScores = [0, 40, 100, 300, 1200] // Single, Double, Triple, Tetris
  const baseScore = lineScores[linesCleared] || 0
  
  score.value += baseScore * level.value
  lines.value += linesCleared
  
  // Level up every 10 lines
  const newLevel = Math.floor(lines.value / 10) + 1
  if (newLevel > level.value) {
    level.value = newLevel
    updateDropSpeed()
  }
}

// Hold current piece
const holdCurrentPiece = () => {
  if (!canHold.value || !currentPiece.value || isPaused.value || gameResult.value) return
  
  const temp = holdPiece.value
  holdPiece.value = {
    ...currentPiece.value,
    x: Math.floor(BOARD_WIDTH / 2) - Math.floor(currentPiece.value.shape[0].length / 2),
    y: 0
  }
  
  if (temp) {
    currentPiece.value = temp
    currentPiece.value.x = Math.floor(BOARD_WIDTH / 2) - Math.floor(temp.shape[0].length / 2)
    currentPiece.value.y = 0
  } else {
    spawnNewPiece()
  }
  
  canHold.value = false
}

// Start drop timer
const startDropTimer = () => {
  if (dropTimer.value) {
    clearInterval(dropTimer.value)
  }
  
  const dropSpeed = Math.max(50, 1000 - (level.value - 1) * 100)
  dropTimer.value = setInterval(() => {
    if (!isPaused.value && !gameResult.value) {
      dropPieceOneRow()
    }
  }, dropSpeed)
}

// Update drop speed
const updateDropSpeed = () => {
  startDropTimer()
}

// Pause/resume game
const pauseGame = () => {
  isPaused.value = !isPaused.value
}

// Game over
const gameOver = () => {
  if (dropTimer.value) {
    clearInterval(dropTimer.value)
    dropTimer.value = null
  }
  
  gameResult.value = {
    title: 'Game Over!',
    message: `Final Score: ${score.value} | Lines: ${lines.value} | Level: ${level.value}`
  }
}

// Reset game
const resetGame = () => {
  if (dropTimer.value) {
    clearInterval(dropTimer.value)
    dropTimer.value = null
  }
  initializeGame()
}

// Get piece color
const getPieceColor = (type: number): string => {
  return COLORS[type] || '#000000'
}

// Keyboard controls
const handleKeyDown = (event: KeyboardEvent) => {
  if (gameResult.value) return
  
  switch (event.code) {
    case 'ArrowLeft':
      event.preventDefault()
      movePiece(-1, 0)
      break
    case 'ArrowRight':
      event.preventDefault()
      movePiece(1, 0)
      break
    case 'ArrowDown':
      event.preventDefault()
      if (movePiece(0, 1)) {
        score.value += 1 // Bonus for soft drop
      }
      break
    case 'ArrowUp':
      event.preventDefault()
      rotatePiece()
      break
    case 'Space':
      event.preventDefault()
      dropPiece()
      break
    case 'KeyC':
      event.preventDefault()
      holdCurrentPiece()
      break
    case 'KeyP':
      event.preventDefault()
      pauseGame()
      break
  }
}

onMounted(() => {
  initializeGame()
  window.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  if (dropTimer.value) {
    clearInterval(dropTimer.value)
  }
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.game-layout {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

.tetris-board {
  display: grid;
  grid-template-rows: repeat(20, 1fr);
  gap: 1px;
  background-color: #1f2937;
  border: 3px solid #374151;
  border-radius: 8px;
  padding: 4px;
}

.tetris-row {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 1px;
}

.tetris-cell {
  width: 25px;
  height: 25px;
  background-color: #111827;
  border: 1px solid #374151;
  transition: all 0.1s ease;
}

.tetris-cell.filled {
  border: 1px solid rgba(255,255,255,0.3);
  box-shadow: inset 0 1px 2px rgba(255,255,255,0.2);
}

.tetris-cell.ghost {
  background-color: rgba(255,255,255,0.1) !important;
  border: 1px dashed rgba(255,255,255,0.3);
}

.next-piece-container,
.hold-piece-container {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-width: 120px;
}

.next-piece-preview,
.hold-piece-preview {
  display: grid;
  grid-template-rows: repeat(4, 1fr);
  gap: 1px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px;
  width: 80px;
  height: 80px;
}

.preview-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
}

.preview-cell {
  width: 16px;
  height: 16px;
  background-color: #e5e7eb;
  border-radius: 2px;
}

.preview-cell.filled {
  border: 1px solid rgba(255,255,255,0.3);
  box-shadow: inset 0 1px 1px rgba(255,255,255,0.2);
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-sm {
  @apply px-2 py-1 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

@media (max-width: 768px) {
  .game-layout {
    flex-direction: column;
    align-items: center;
  }
  
  .next-piece-container,
  .hold-piece-container {
    order: 2;
  }
  
  .tetris-board {
    order: 1;
  }
}
</style>
