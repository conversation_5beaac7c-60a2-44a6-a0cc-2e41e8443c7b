<template>
  <div class="collapse-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.collapse.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ blocksRemaining }}</div>
          <div class="text-sm text-gray-500">Blocks</div>
        </div>
      </div>
      <div class="mt-2">
        <div class="bg-gray-200 rounded-full h-2 w-64 mx-auto">
          <div 
            class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: Math.max(0, 100 - (blocksRemaining / totalBlocks * 100)) + '%' }"
          ></div>
        </div>
        <p class="text-sm text-gray-600 mt-1">{{ Math.round((1 - blocksRemaining / totalBlocks) * 100) }}% cleared</p>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="collapse-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <div 
        v-for="(row, rowIndex) in board" 
        :key="rowIndex"
        class="collapse-row"
      >
        <div
          v-for="(block, colIndex) in row"
          :key="colIndex"
          class="collapse-cell"
          :class="{
            'highlighted': highlightedBlocks.some(pos => pos.row === rowIndex && pos.col === colIndex),
            'collapsing': block && block.collapsing,
            'falling': block && block.falling
          }"
          @click="handleBlockClick(rowIndex, colIndex)"
          @mouseenter="highlightGroup(rowIndex, colIndex)"
          @mouseleave="clearHighlight"
        >
          <div v-if="block" class="block" :style="{ backgroundColor: getBlockColor(block.type) }">
            <div class="block-inner">
              <div class="block-shine"></div>
              <div class="block-symbol">{{ getBlockSymbol(block.type) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="shuffleBlocks" class="btn btn-primary" :disabled="!canShuffle">
        Shuffle
      </button>
      <button @click="addNewRow" class="btn btn-primary" :disabled="!canAddRow">
        Add Row
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'

// Types
interface Block {
  type: number
  collapsing?: boolean
  falling?: boolean
}

interface Position {
  row: number
  col: number
}

interface GameResult {
  title: string
  message: string
}

// Constants
const BOARD_WIDTH = 12
const BOARD_HEIGHT = 10
const BLOCK_TYPES = 5
const COLORS = ['#ef4444', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6']
const SYMBOLS = ['■', '●', '▲', '♦', '★']

// Reactive state
const board = ref<(Block | null)[][]>([])
const highlightedBlocks = ref<Position[]>([])
const score = ref(0)
const level = ref(1)
const gameResult = ref<GameResult | null>(null)
const boardSize = ref(480)
const canShuffle = ref(true)
const canAddRow = ref(true)
const totalBlocks = ref(0)

// Computed
const blocksRemaining = computed(() => {
  return board.value.flat().filter(block => block !== null).length
})

const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (highlightedBlocks.value.length > 1) {
    return `Click to remove ${highlightedBlocks.value.length} blocks`
  }
  return 'Click on groups of 2 or more connected blocks of the same color!'
})

// Initialize board
const initializeBoard = () => {
  board.value = Array(BOARD_HEIGHT).fill(null).map(() => Array(BOARD_WIDTH).fill(null))
  
  // Fill bottom rows with random blocks
  const initialRows = 6 + level.value
  for (let row = BOARD_HEIGHT - initialRows; row < BOARD_HEIGHT; row++) {
    for (let col = 0; col < BOARD_WIDTH; col++) {
      board.value[row][col] = {
        type: Math.floor(Math.random() * BLOCK_TYPES)
      }
    }
  }
  
  totalBlocks.value = blocksRemaining.value
  highlightedBlocks.value = []
  score.value = 0
  level.value = 1
  gameResult.value = null
  canShuffle.value = true
  canAddRow.value = true
}

// Handle block click
const handleBlockClick = async (row: number, col: number) => {
  if (gameResult.value || !board.value[row][col]) return
  
  const group = findConnectedGroup(row, col)
  
  if (group.length >= 2) {
    await collapseGroup(group)
    
    // Check win condition
    if (blocksRemaining.value === 0) {
      levelComplete()
    } else if (!hasValidMoves()) {
      gameOver()
    }
  }
}

// Find connected group of same-colored blocks
const findConnectedGroup = (startRow: number, startCol: number): Position[] => {
  const block = board.value[startRow][startCol]
  if (!block) return []
  
  const visited = Array(BOARD_HEIGHT).fill(null).map(() => Array(BOARD_WIDTH).fill(false))
  const group: Position[] = []
  const queue: Position[] = [{ row: startRow, col: startCol }]
  
  while (queue.length > 0) {
    const { row, col } = queue.shift()!
    
    if (visited[row][col]) continue
    visited[row][col] = true
    
    const currentBlock = board.value[row][col]
    if (!currentBlock || currentBlock.type !== block.type) continue
    
    group.push({ row, col })
    
    // Check adjacent cells
    const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
    for (const [dr, dc] of directions) {
      const newRow = row + dr
      const newCol = col + dc
      
      if (newRow >= 0 && newRow < BOARD_HEIGHT && 
          newCol >= 0 && newCol < BOARD_WIDTH && 
          !visited[newRow][newCol]) {
        queue.push({ row: newRow, col: newCol })
      }
    }
  }
  
  return group
}

// Highlight group on hover
const highlightGroup = (row: number, col: number) => {
  if (gameResult.value || !board.value[row][col]) {
    highlightedBlocks.value = []
    return
  }
  
  const group = findConnectedGroup(row, col)
  if (group.length >= 2) {
    highlightedBlocks.value = group
  } else {
    highlightedBlocks.value = []
  }
}

// Clear highlight
const clearHighlight = () => {
  highlightedBlocks.value = []
}

// Collapse group of blocks
const collapseGroup = async (group: Position[]) => {
  // Mark blocks as collapsing
  for (const pos of group) {
    const block = board.value[pos.row][pos.col]
    if (block) {
      block.collapsing = true
    }
  }
  
  await nextTick()
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // Remove blocks
  for (const pos of group) {
    board.value[pos.row][pos.col] = null
  }
  
  // Update score
  const baseScore = group.length * group.length * 10
  const levelBonus = level.value * 5
  score.value += baseScore + levelBonus
  
  // Apply gravity
  await applyGravity()
  
  // Remove empty columns
  removeEmptyColumns()
  
  highlightedBlocks.value = []
}

// Apply gravity to make blocks fall
const applyGravity = async () => {
  let moved = false
  
  for (let col = 0; col < BOARD_WIDTH; col++) {
    const column = []
    
    // Collect non-null blocks from bottom to top
    for (let row = BOARD_HEIGHT - 1; row >= 0; row--) {
      if (board.value[row][col]) {
        column.push(board.value[row][col])
      }
    }
    
    // Clear column
    for (let row = 0; row < BOARD_HEIGHT; row++) {
      board.value[row][col] = null
    }
    
    // Place blocks at bottom
    for (let i = 0; i < column.length; i++) {
      const block = column[i]!
      block.falling = true
      board.value[BOARD_HEIGHT - 1 - i][col] = block
      moved = true
    }
  }
  
  if (moved) {
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Remove falling flag
    for (let row = 0; row < BOARD_HEIGHT; row++) {
      for (let col = 0; col < BOARD_WIDTH; col++) {
        const block = board.value[row][col]
        if (block) {
          block.falling = false
          block.collapsing = false
        }
      }
    }
  }
}

// Remove empty columns by shifting blocks left
const removeEmptyColumns = () => {
  const newBoard: (Block | null)[][] = Array(BOARD_HEIGHT).fill(null).map(() => Array(BOARD_WIDTH).fill(null))
  let newCol = 0
  
  for (let col = 0; col < BOARD_WIDTH; col++) {
    // Check if column has any blocks
    let hasBlocks = false
    for (let row = 0; row < BOARD_HEIGHT; row++) {
      if (board.value[row][col]) {
        hasBlocks = true
        break
      }
    }
    
    if (hasBlocks) {
      // Copy column to new position
      for (let row = 0; row < BOARD_HEIGHT; row++) {
        newBoard[row][newCol] = board.value[row][col]
      }
      newCol++
    }
  }
  
  board.value = newBoard
}

// Check if there are valid moves
const hasValidMoves = (): boolean => {
  for (let row = 0; row < BOARD_HEIGHT; row++) {
    for (let col = 0; col < BOARD_WIDTH; col++) {
      if (board.value[row][col]) {
        const group = findConnectedGroup(row, col)
        if (group.length >= 2) {
          return true
        }
      }
    }
  }
  return false
}

// Shuffle blocks
const shuffleBlocks = () => {
  if (!canShuffle.value) return
  
  const blocks: Block[] = []
  
  // Collect all blocks
  for (let row = 0; row < BOARD_HEIGHT; row++) {
    for (let col = 0; col < BOARD_WIDTH; col++) {
      if (board.value[row][col]) {
        blocks.push(board.value[row][col]!)
      }
    }
  }
  
  // Shuffle block types
  for (let i = blocks.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    const tempType = blocks[i].type
    blocks[i].type = blocks[j].type
    blocks[j].type = tempType
  }
  
  highlightedBlocks.value = []
  canShuffle.value = false
  
  // Re-enable shuffle after delay
  setTimeout(() => {
    canShuffle.value = true
  }, 30000)
}

// Add new row of blocks
const addNewRow = () => {
  if (!canAddRow.value) return
  
  // Shift all blocks up
  for (let row = 0; row < BOARD_HEIGHT - 1; row++) {
    for (let col = 0; col < BOARD_WIDTH; col++) {
      board.value[row][col] = board.value[row + 1][col]
    }
  }
  
  // Add new row at bottom
  for (let col = 0; col < BOARD_WIDTH; col++) {
    board.value[BOARD_HEIGHT - 1][col] = {
      type: Math.floor(Math.random() * BLOCK_TYPES)
    }
  }
  
  totalBlocks.value = blocksRemaining.value
  canAddRow.value = false
  
  // Re-enable add row after delay
  setTimeout(() => {
    canAddRow.value = true
  }, 20000)
  
  // Check if game over (blocks reached top)
  for (let col = 0; col < BOARD_WIDTH; col++) {
    if (board.value[0][col]) {
      gameOver()
      return
    }
  }
}

// Level complete
const levelComplete = () => {
  level.value++
  score.value += 1000 * level.value
  
  gameResult.value = {
    title: 'Level Complete!',
    message: `Great job! Moving to level ${level.value}`
  }
  
  setTimeout(() => {
    gameResult.value = null
    initializeBoard()
  }, 2000)
}

// Game over
const gameOver = () => {
  gameResult.value = {
    title: 'Game Over!',
    message: `Final Score: ${score.value} | Level: ${level.value}`
  }
}

// Get block color
const getBlockColor = (type: number): string => {
  return COLORS[type] || '#gray'
}

// Get block symbol
const getBlockSymbol = (type: number): string => {
  return SYMBOLS[type] || '■'
}

// Reset game
const resetGame = () => {
  initializeBoard()
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 600)
  boardSize.value = Math.max(360, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.collapse-board {
  display: grid;
  grid-template-rows: repeat(10, 1fr);
  gap: 1px;
  background-color: #1f2937;
  border: 3px solid #374151;
  border-radius: 8px;
  padding: 4px;
}

.collapse-row {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1px;
}

.collapse-cell {
  aspect-ratio: 1;
  background-color: #111827;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.collapse-cell:hover {
  background-color: #1f2937;
}

.collapse-cell.highlighted {
  background-color: #3b82f6;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

.collapse-cell.collapsing .block {
  animation: collapse 0.3s ease-in forwards;
}

.collapse-cell.falling .block {
  animation: fall 0.3s ease-out;
}

.block {
  width: 90%;
  height: 90%;
  border-radius: 4px;
  border: 1px solid rgba(255,255,255,0.2);
  box-shadow: 
    inset 0 1px 2px rgba(255,255,255,0.3),
    0 1px 4px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.block-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.block-shine {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 30%;
  height: 30%;
  background: rgba(255,255,255,0.4);
  border-radius: 50%;
  filter: blur(2px);
}

.block-symbol {
  font-size: 1em;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  z-index: 2;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

@keyframes collapse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

@keyframes fall {
  from {
    transform: translateY(-50px);
    opacity: 0.7;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
