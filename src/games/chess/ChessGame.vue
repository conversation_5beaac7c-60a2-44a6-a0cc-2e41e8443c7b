<template>
  <div class="chess-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.chess.name') }}</h2>
      <div class="flex justify-center items-center space-x-4">
        <span class="text-lg font-medium" :class="currentPlayer === 'white' ? 'text-blue-600' : 'text-gray-500'">
          White
        </span>
        <span class="text-gray-400">vs</span>
        <span class="text-lg font-medium" :class="currentPlayer === 'black' ? 'text-blue-600' : 'text-gray-500'">
          Black
        </span>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Chess Board -->
    <div class="chess-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <div 
        v-for="(row, rowIndex) in board" 
        :key="rowIndex"
        class="chess-row"
      >
        <div
          v-for="(cell, colIndex) in row"
          :key="colIndex"
          class="chess-cell"
          :class="{
            'light': (rowIndex + colIndex) % 2 === 0,
            'dark': (rowIndex + colIndex) % 2 === 1,
            'selected': selectedSquare && selectedSquare.row === rowIndex && selectedSquare.col === colIndex,
            'possible-move': isPossibleMove(rowIndex, colIndex),
            'last-move': isLastMove(rowIndex, colIndex)
          }"
          @click="handleSquareClick(rowIndex, colIndex)"
        >
          <!-- Piece -->
          <div v-if="cell" class="chess-piece" :class="cell.color">
            {{ getPieceSymbol(cell) }}
          </div>
          
          <!-- Coordinates -->
          <div v-if="colIndex === 0" class="rank-label">{{ 8 - rowIndex }}</div>
          <div v-if="rowIndex === 7" class="file-label">{{ String.fromCharCode(97 + colIndex) }}</div>
        </div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        {{ $t('common.retry') }}
      </button>
      <button @click="undoMove" class="btn btn-secondary" :disabled="moveHistory.length === 0">
        Undo
      </button>
    </div>

    <!-- Move History -->
    <div v-if="moveHistory.length > 0" class="mt-6 bg-white rounded-lg p-4 max-w-md mx-auto">
      <h3 class="font-semibold text-gray-900 mb-2">Move History</h3>
      <div class="max-h-32 overflow-y-auto text-sm">
        <div v-for="(move, index) in moveHistory" :key="index" class="text-gray-600">
          {{ index + 1 }}. {{ move }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Types
interface Piece {
  type: 'king' | 'queen' | 'rook' | 'bishop' | 'knight' | 'pawn'
  color: 'white' | 'black'
}

interface Square {
  row: number
  col: number
}

interface Move {
  from: Square
  to: Square
  piece: Piece
  captured?: Piece
}

// Reactive state
const board = ref<(Piece | null)[][]>([])
const currentPlayer = ref<'white' | 'black'>('white')
const selectedSquare = ref<Square | null>(null)
const possibleMoves = ref<Square[]>([])
const moveHistory = ref<string[]>([])
const lastMove = ref<Move | null>(null)
const boardSize = ref(400)

// Computed
const gameStatus = computed(() => {
  return `${currentPlayer.value === 'white' ? 'White' : 'Black'} to move`
})

// Initialize board
const initializeBoard = () => {
  const newBoard: (Piece | null)[][] = Array(8).fill(null).map(() => Array(8).fill(null))
  
  // Place pieces
  const pieceOrder: Piece['type'][] = ['rook', 'knight', 'bishop', 'queen', 'king', 'bishop', 'knight', 'rook']
  
  // Black pieces
  for (let col = 0; col < 8; col++) {
    newBoard[0][col] = { type: pieceOrder[col], color: 'black' }
    newBoard[1][col] = { type: 'pawn', color: 'black' }
  }
  
  // White pieces
  for (let col = 0; col < 8; col++) {
    newBoard[6][col] = { type: 'pawn', color: 'white' }
    newBoard[7][col] = { type: pieceOrder[col], color: 'white' }
  }
  
  board.value = newBoard
}

// Get piece symbol
const getPieceSymbol = (piece: Piece): string => {
  const symbols = {
    white: {
      king: '♔',
      queen: '♕',
      rook: '♖',
      bishop: '♗',
      knight: '♘',
      pawn: '♙'
    },
    black: {
      king: '♚',
      queen: '♛',
      rook: '♜',
      bishop: '♝',
      knight: '♞',
      pawn: '♟'
    }
  }
  
  return symbols[piece.color][piece.type]
}

// Check if square is a possible move
const isPossibleMove = (row: number, col: number): boolean => {
  return possibleMoves.value.some(move => move.row === row && move.col === col)
}

// Check if square is part of last move
const isLastMove = (row: number, col: number): boolean => {
  if (!lastMove.value) return false
  return (lastMove.value.from.row === row && lastMove.value.from.col === col) ||
         (lastMove.value.to.row === row && lastMove.value.to.col === col)
}

// Get possible moves for a piece (simplified)
const getPossibleMoves = (row: number, col: number): Square[] => {
  const piece = board.value[row][col]
  if (!piece || piece.color !== currentPlayer.value) return []
  
  const moves: Square[] = []
  
  // Simplified move generation for demo
  switch (piece.type) {
    case 'pawn':
      const direction = piece.color === 'white' ? -1 : 1
      const startRow = piece.color === 'white' ? 6 : 1
      
      // Forward move
      if (row + direction >= 0 && row + direction < 8 && !board.value[row + direction][col]) {
        moves.push({ row: row + direction, col })
        
        // Double move from start
        if (row === startRow && !board.value[row + 2 * direction][col]) {
          moves.push({ row: row + 2 * direction, col })
        }
      }
      
      // Captures
      for (const dcol of [-1, 1]) {
        const newCol = col + dcol
        if (newCol >= 0 && newCol < 8 && row + direction >= 0 && row + direction < 8) {
          const target = board.value[row + direction][newCol]
          if (target && target.color !== piece.color) {
            moves.push({ row: row + direction, col: newCol })
          }
        }
      }
      break
      
    case 'rook':
      // Horizontal and vertical moves
      for (const [dr, dc] of [[0, 1], [0, -1], [1, 0], [-1, 0]]) {
        for (let i = 1; i < 8; i++) {
          const newRow = row + dr * i
          const newCol = col + dc * i
          
          if (newRow < 0 || newRow >= 8 || newCol < 0 || newCol >= 8) break
          
          const target = board.value[newRow][newCol]
          if (!target) {
            moves.push({ row: newRow, col: newCol })
          } else {
            if (target.color !== piece.color) {
              moves.push({ row: newRow, col: newCol })
            }
            break
          }
        }
      }
      break
      
    case 'knight':
      const knightMoves = [[-2, -1], [-2, 1], [-1, -2], [-1, 2], [1, -2], [1, 2], [2, -1], [2, 1]]
      for (const [dr, dc] of knightMoves) {
        const newRow = row + dr
        const newCol = col + dc
        
        if (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
          const target = board.value[newRow][newCol]
          if (!target || target.color !== piece.color) {
            moves.push({ row: newRow, col: newCol })
          }
        }
      }
      break
      
    case 'bishop':
      // Diagonal moves
      for (const [dr, dc] of [[1, 1], [1, -1], [-1, 1], [-1, -1]]) {
        for (let i = 1; i < 8; i++) {
          const newRow = row + dr * i
          const newCol = col + dc * i
          
          if (newRow < 0 || newRow >= 8 || newCol < 0 || newCol >= 8) break
          
          const target = board.value[newRow][newCol]
          if (!target) {
            moves.push({ row: newRow, col: newCol })
          } else {
            if (target.color !== piece.color) {
              moves.push({ row: newRow, col: newCol })
            }
            break
          }
        }
      }
      break
      
    case 'queen':
      // Combination of rook and bishop moves
      for (const [dr, dc] of [[0, 1], [0, -1], [1, 0], [-1, 0], [1, 1], [1, -1], [-1, 1], [-1, -1]]) {
        for (let i = 1; i < 8; i++) {
          const newRow = row + dr * i
          const newCol = col + dc * i
          
          if (newRow < 0 || newRow >= 8 || newCol < 0 || newCol >= 8) break
          
          const target = board.value[newRow][newCol]
          if (!target) {
            moves.push({ row: newRow, col: newCol })
          } else {
            if (target.color !== piece.color) {
              moves.push({ row: newRow, col: newCol })
            }
            break
          }
        }
      }
      break
      
    case 'king':
      // One square in any direction
      for (const [dr, dc] of [[0, 1], [0, -1], [1, 0], [-1, 0], [1, 1], [1, -1], [-1, 1], [-1, -1]]) {
        const newRow = row + dr
        const newCol = col + dc
        
        if (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
          const target = board.value[newRow][newCol]
          if (!target || target.color !== piece.color) {
            moves.push({ row: newRow, col: newCol })
          }
        }
      }
      break
  }
  
  return moves
}

// Handle square click
const handleSquareClick = (row: number, col: number) => {
  const piece = board.value[row][col]
  
  if (selectedSquare.value) {
    // Try to make a move
    if (isPossibleMove(row, col)) {
      makeMove(selectedSquare.value, { row, col })
    }
    
    // Deselect
    selectedSquare.value = null
    possibleMoves.value = []
  } else if (piece && piece.color === currentPlayer.value) {
    // Select piece
    selectedSquare.value = { row, col }
    possibleMoves.value = getPossibleMoves(row, col)
  }
}

// Make a move
const makeMove = (from: Square, to: Square) => {
  const piece = board.value[from.row][from.col]
  const captured = board.value[to.row][to.col]
  
  if (!piece) return
  
  // Move piece
  board.value[to.row][to.col] = piece
  board.value[from.row][from.col] = null
  
  // Record move
  const moveNotation = `${String.fromCharCode(97 + from.col)}${8 - from.row}-${String.fromCharCode(97 + to.col)}${8 - to.row}`
  moveHistory.value.push(moveNotation)
  
  lastMove.value = { from, to, piece, captured: captured || undefined }
  
  // Switch player
  currentPlayer.value = currentPlayer.value === 'white' ? 'black' : 'white'
}

// Reset game
const resetGame = () => {
  initializeBoard()
  currentPlayer.value = 'white'
  selectedSquare.value = null
  possibleMoves.value = []
  moveHistory.value = []
  lastMove.value = null
}

// Undo move
const undoMove = () => {
  if (moveHistory.value.length === 0 || !lastMove.value) return
  
  // Restore pieces
  board.value[lastMove.value.from.row][lastMove.value.from.col] = lastMove.value.piece
  board.value[lastMove.value.to.row][lastMove.value.to.col] = lastMove.value.captured || null
  
  // Remove from history
  moveHistory.value.pop()
  
  // Switch player back
  currentPlayer.value = currentPlayer.value === 'white' ? 'black' : 'white'
  
  // Clear selection
  selectedSquare.value = null
  possibleMoves.value = []
  lastMove.value = null
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 600)
  boardSize.value = Math.max(320, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.chess-board {
  border: 2px solid #8B4513;
  position: relative;
  margin: 0 auto;
}

.chess-row {
  display: flex;
  height: 12.5%;
}

.chess-cell {
  width: 12.5%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chess-cell.light {
  background-color: #f0d9b5;
}

.chess-cell.dark {
  background-color: #b58863;
}

.chess-cell.selected {
  background-color: #ffff00 !important;
  box-shadow: inset 0 0 0 3px #ff6b6b;
}

.chess-cell.possible-move {
  background-color: #90EE90 !important;
}

.chess-cell.possible-move::after {
  content: '';
  position: absolute;
  width: 20%;
  height: 20%;
  background-color: rgba(0, 128, 0, 0.6);
  border-radius: 50%;
}

.chess-cell.last-move {
  background-color: #ffeb3b !important;
}

.chess-piece {
  font-size: 2rem;
  user-select: none;
  transition: transform 0.1s ease;
}

.chess-piece:hover {
  transform: scale(1.1);
}

.rank-label {
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 0.7rem;
  font-weight: bold;
  color: #8B4513;
  pointer-events: none;
}

.file-label {
  position: absolute;
  bottom: 2px;
  right: 2px;
  font-size: 0.7rem;
  font-weight: bold;
  color: #8B4513;
  pointer-events: none;
}

@media (max-width: 640px) {
  .chess-piece {
    font-size: 1.5rem;
  }
  
  .rank-label,
  .file-label {
    font-size: 0.6rem;
  }
}
</style>
