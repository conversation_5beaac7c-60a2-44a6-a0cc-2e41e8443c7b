<template>
  <div class="zuma-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.zuma.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-red-600">{{ marbleChain.length }}</div>
          <div class="text-sm text-gray-500">Marbles</div>
        </div>
      </div>
      <div class="mt-2">
        <div class="bg-red-200 rounded-full h-2 w-64 mx-auto">
          <div 
            class="bg-gradient-to-r from-green-500 to-red-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: Math.max(0, 100 - (chainProgress * 100)) + '%' }"
          ></div>
        </div>
        <p class="text-sm text-gray-600 mt-1">Don't let the marbles reach the end!</p>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="zuma-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <!-- Track path -->
      <svg class="track-path" :width="boardSize" :height="boardSize">
        <path
          :d="trackPath"
          stroke="#8b5cf6"
          stroke-width="40"
          fill="none"
          stroke-linecap="round"
          opacity="0.3"
        />
        <path
          :d="trackPath"
          stroke="#a855f7"
          stroke-width="36"
          fill="none"
          stroke-linecap="round"
          opacity="0.5"
        />
        <path
          :d="trackPath"
          stroke="#c084fc"
          stroke-width="32"
          fill="none"
          stroke-linecap="round"
        />
      </svg>

      <!-- Marble chain -->
      <div
        v-for="(marble, index) in marbleChain"
        :key="marble.id"
        class="marble"
        :class="{ 'matching': marble.matching }"
        :style="{
          left: marble.x - 15 + 'px',
          top: marble.y - 15 + 'px',
          backgroundColor: getMarbleColor(marble.color),
          zIndex: marbleChain.length - index
        }"
      >
        <div class="marble-inner">
          <div class="marble-shine"></div>
          <div class="marble-symbol">{{ getMarbleSymbol(marble.color) }}</div>
        </div>
      </div>

      <!-- Shooting marble -->
      <div
        v-if="shootingMarble"
        class="marble shooting"
        :style="{
          left: shootingMarble.x - 15 + 'px',
          top: shootingMarble.y - 15 + 'px',
          backgroundColor: getMarbleColor(shootingMarble.color)
        }"
      >
        <div class="marble-inner">
          <div class="marble-shine"></div>
          <div class="marble-symbol">{{ getMarbleSymbol(shootingMarble.color) }}</div>
        </div>
      </div>

      <!-- Shooter -->
      <div class="shooter" :style="{ left: shooterX + 'px', top: shooterY + 'px', transform: `rotate(${shooterAngle}deg)` }">
        <div class="shooter-base"></div>
        <div class="shooter-barrel"></div>
        <div
          class="current-marble marble"
          :style="{ backgroundColor: getMarbleColor(currentMarble?.color || 0) }"
        >
          <div class="marble-inner">
            <div class="marble-shine"></div>
            <div class="marble-symbol">{{ getMarbleSymbol(currentMarble?.color || 0) }}</div>
          </div>
        </div>
      </div>

      <!-- Next marble preview -->
      <div class="next-marble-preview" :style="{ left: (shooterX + 50) + 'px', top: (shooterY + 10) + 'px' }">
        <div class="text-xs text-gray-500 mb-1">Next</div>
        <div
          class="marble small"
          :style="{ backgroundColor: getMarbleColor(nextMarble?.color || 0) }"
        >
          <div class="marble-inner">
            <div class="marble-shine"></div>
            <div class="marble-symbol">{{ getMarbleSymbol(nextMarble?.color || 0) }}</div>
          </div>
        </div>
      </div>

      <!-- Aim line -->
      <svg v-if="aimLine" class="aim-line" :width="boardSize" :height="boardSize">
        <line
          :x1="aimLine.startX"
          :y1="aimLine.startY"
          :x2="aimLine.endX"
          :y2="aimLine.endY"
          stroke="#3b82f6"
          stroke-width="2"
          stroke-dasharray="5,5"
          opacity="0.7"
        />
      </svg>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="pauseGame" class="btn btn-primary" v-if="!gameResult">
        {{ isPaused ? 'Resume' : 'Pause' }}
      </button>
    </div>

    <!-- Controls Help -->
    <div class="mt-4 text-center text-sm text-gray-600">
      <p>Move mouse to aim, click to shoot marbles</p>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// Types
interface Marble {
  id: number
  color: number
  x: number
  y: number
  position: number
  matching?: boolean
}

interface ShootingMarble {
  x: number
  y: number
  color: number
  vx: number
  vy: number
}

interface AimLine {
  startX: number
  startY: number
  endX: number
  endY: number
}

interface GameResult {
  title: string
  message: string
}

// Constants
const MARBLE_COLORS = 6
const COLORS = ['#ef4444', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#f97316']
const SYMBOLS = ['🔴', '🔵', '🟢', '🟡', '🟣', '🟠']
const MARBLE_SIZE = 30
const SHOOTING_SPEED = 8
const CHAIN_SPEED = 0.5

// Reactive state
const marbleChain = ref<Marble[]>([])
const shootingMarble = ref<ShootingMarble | null>(null)
const currentMarble = ref<{ color: number } | null>(null)
const nextMarble = ref<{ color: number } | null>(null)
const score = ref(0)
const level = ref(1)
const gameResult = ref<GameResult | null>(null)
const isPaused = ref(false)
const boardSize = ref(500)
const shooterX = ref(250)
const shooterY = ref(400)
const shooterAngle = ref(0)
const aimLine = ref<AimLine | null>(null)
const mouseX = ref(0)
const mouseY = ref(0)
const animationId = ref<number | null>(null)
const chainProgress = ref(0)
const trackPath = ref('')
const pathLength = ref(0)
const marbleIdCounter = ref(0)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (isPaused.value) return 'Game Paused'
  return 'Shoot marbles to match 3 or more of the same color!'
})

// Initialize game
const initializeGame = () => {
  marbleChain.value = []
  score.value = 0
  level.value = 1
  gameResult.value = null
  isPaused.value = false
  shootingMarble.value = null
  chainProgress.value = 0
  marbleIdCounter.value = 0
  
  // Create track path
  createTrackPath()
  
  // Initialize marble chain
  createInitialChain()
  
  // Set up shooter
  currentMarble.value = { color: getRandomColor() }
  nextMarble.value = { color: getRandomColor() }
  
  updateShooterPosition()
  startGameLoop()
}

// Create spiral track path
const createTrackPath = () => {
  const centerX = boardSize.value / 2
  const centerY = boardSize.value / 2
  const maxRadius = Math.min(centerX, centerY) - 50
  const turns = 3
  const points: string[] = []
  
  for (let i = 0; i <= 360 * turns; i += 5) {
    const angle = (i * Math.PI) / 180
    const radius = maxRadius * (1 - i / (360 * turns))
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    
    if (i === 0) {
      points.push(`M ${x} ${y}`)
    } else {
      points.push(`L ${x} ${y}`)
    }
  }
  
  trackPath.value = points.join(' ')
  
  // Calculate path length for positioning marbles
  pathLength.value = 360 * turns * 2 // Approximate
}

// Create initial marble chain
const createInitialChain = () => {
  const initialMarbles = 20 + level.value * 5
  
  for (let i = 0; i < initialMarbles; i++) {
    const position = i * MARBLE_SIZE
    const coords = getPositionOnPath(position)
    
    marbleChain.value.push({
      id: marbleIdCounter.value++,
      color: getRandomColor(),
      x: coords.x,
      y: coords.y,
      position
    })
  }
}

// Get position on track path
const getPositionOnPath = (position: number): { x: number, y: number } => {
  const centerX = boardSize.value / 2
  const centerY = boardSize.value / 2
  const maxRadius = Math.min(centerX, centerY) - 50
  const turns = 3
  
  const progress = position / pathLength.value
  const angle = progress * 360 * turns * Math.PI / 180
  const radius = maxRadius * (1 - progress)
  
  return {
    x: centerX + radius * Math.cos(angle),
    y: centerY + radius * Math.sin(angle)
  }
}

// Get random marble color
const getRandomColor = (): number => {
  return Math.floor(Math.random() * MARBLE_COLORS)
}

// Get marble color
const getMarbleColor = (color: number): string => {
  return COLORS[color] || '#gray'
}

// Get marble symbol
const getMarbleSymbol = (color: number): string => {
  return SYMBOLS[color] || '⚫'
}

// Update shooter position
const updateShooterPosition = () => {
  shooterX.value = boardSize.value / 2 - 15
  shooterY.value = boardSize.value - 80
}

// Handle mouse move for aiming
const handleMouseMove = (event: MouseEvent) => {
  if (gameResult.value || isPaused.value || shootingMarble.value) return
  
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  mouseX.value = event.clientX - rect.left
  mouseY.value = event.clientY - rect.top
  
  updateAiming()
}

// Update aiming
const updateAiming = () => {
  const dx = mouseX.value - (shooterX.value + 15)
  const dy = mouseY.value - (shooterY.value + 15)
  
  shooterAngle.value = Math.atan2(dy, dx) * 180 / Math.PI
  
  // Update aim line
  const length = 100
  const angle = Math.atan2(dy, dx)
  
  aimLine.value = {
    startX: shooterX.value + 15,
    startY: shooterY.value + 15,
    endX: shooterX.value + 15 + Math.cos(angle) * length,
    endY: shooterY.value + 15 + Math.sin(angle) * length
  }
}

// Handle click to shoot
const handleClick = (event: MouseEvent) => {
  if (gameResult.value || isPaused.value || shootingMarble.value) return
  
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const targetX = event.clientX - rect.left
  const targetY = event.clientY - rect.top
  
  shootMarble(targetX, targetY)
}

// Shoot marble
const shootMarble = (targetX: number, targetY: number) => {
  if (!currentMarble.value) return
  
  const startX = shooterX.value + 15
  const startY = shooterY.value + 15
  
  const dx = targetX - startX
  const dy = targetY - startY
  const magnitude = Math.sqrt(dx * dx + dy * dy)
  
  const vx = (dx / magnitude) * SHOOTING_SPEED
  const vy = (dy / magnitude) * SHOOTING_SPEED
  
  shootingMarble.value = {
    x: startX,
    y: startY,
    color: currentMarble.value.color,
    vx,
    vy
  }
  
  // Move to next marble
  currentMarble.value = nextMarble.value
  nextMarble.value = { color: getRandomColor() }
  
  aimLine.value = null
}

// Start game loop
const startGameLoop = () => {
  const gameLoop = () => {
    if (!isPaused.value && !gameResult.value) {
      updateGame()
    }
    animationId.value = requestAnimationFrame(gameLoop)
  }
  gameLoop()
}

// Update game state
const updateGame = () => {
  // Move marble chain forward
  moveChainForward()
  
  // Update shooting marble
  if (shootingMarble.value) {
    updateShootingMarble()
  }
  
  // Check win/lose conditions
  checkGameConditions()
}

// Move chain forward
const moveChainForward = () => {
  for (const marble of marbleChain.value) {
    marble.position += CHAIN_SPEED
    const coords = getPositionOnPath(marble.position)
    marble.x = coords.x
    marble.y = coords.y
  }
  
  // Update chain progress
  if (marbleChain.value.length > 0) {
    const leadMarble = marbleChain.value[marbleChain.value.length - 1]
    chainProgress.value = leadMarble.position / pathLength.value
  }
}

// Update shooting marble
const updateShootingMarble = () => {
  if (!shootingMarble.value) return
  
  const marble = shootingMarble.value
  
  // Update position
  marble.x += marble.vx
  marble.y += marble.vy
  
  // Check bounds
  if (marble.x < 0 || marble.x > boardSize.value || marble.y < 0 || marble.y > boardSize.value) {
    shootingMarble.value = null
    return
  }
  
  // Check collision with chain
  const collisionIndex = checkChainCollision(marble)
  if (collisionIndex !== -1) {
    insertMarbleInChain(marble, collisionIndex)
    shootingMarble.value = null
  }
}

// Check collision with marble chain
const checkChainCollision = (shootingMarble: ShootingMarble): number => {
  for (let i = 0; i < marbleChain.value.length; i++) {
    const chainMarble = marbleChain.value[i]
    const distance = Math.sqrt(
      Math.pow(shootingMarble.x - chainMarble.x, 2) + 
      Math.pow(shootingMarble.y - chainMarble.y, 2)
    )
    
    if (distance < MARBLE_SIZE) {
      return i
    }
  }
  return -1
}

// Insert marble in chain
const insertMarbleInChain = (shootingMarble: ShootingMarble, index: number) => {
  const newMarble: Marble = {
    id: marbleIdCounter.value++,
    color: shootingMarble.color,
    x: shootingMarble.x,
    y: shootingMarble.y,
    position: marbleChain.value[index].position
  }
  
  // Insert marble
  marbleChain.value.splice(index, 0, newMarble)
  
  // Adjust positions
  for (let i = index + 1; i < marbleChain.value.length; i++) {
    marbleChain.value[i].position += MARBLE_SIZE
    const coords = getPositionOnPath(marbleChain.value[i].position)
    marbleChain.value[i].x = coords.x
    marbleChain.value[i].y = coords.y
  }
  
  // Check for matches
  setTimeout(() => {
    checkMatches(index)
  }, 100)
}

// Check for matches
const checkMatches = (startIndex: number) => {
  const color = marbleChain.value[startIndex].color
  let matchStart = startIndex
  let matchEnd = startIndex
  
  // Find start of match
  while (matchStart > 0 && marbleChain.value[matchStart - 1].color === color) {
    matchStart--
  }
  
  // Find end of match
  while (matchEnd < marbleChain.value.length - 1 && marbleChain.value[matchEnd + 1].color === color) {
    matchEnd++
  }
  
  const matchLength = matchEnd - matchStart + 1
  
  if (matchLength >= 3) {
    // Mark matching marbles
    for (let i = matchStart; i <= matchEnd; i++) {
      marbleChain.value[i].matching = true
    }
    
    // Remove after animation
    setTimeout(() => {
      removeMatches(matchStart, matchEnd)
    }, 300)
  }
}

// Remove matching marbles
const removeMatches = (start: number, end: number) => {
  const removedCount = end - start + 1
  marbleChain.value.splice(start, removedCount)
  
  // Update score
  score.value += removedCount * 10 * level.value
  
  // Adjust positions
  for (let i = start; i < marbleChain.value.length; i++) {
    marbleChain.value[i].position -= removedCount * MARBLE_SIZE
    const coords = getPositionOnPath(marbleChain.value[i].position)
    marbleChain.value[i].x = coords.x
    marbleChain.value[i].y = coords.y
  }
  
  // Check for chain reaction
  if (start < marbleChain.value.length && start > 0) {
    if (marbleChain.value[start - 1].color === marbleChain.value[start].color) {
      setTimeout(() => {
        checkMatches(start)
      }, 100)
    }
  }
}

// Check game conditions
const checkGameConditions = () => {
  // Check win condition
  if (marbleChain.value.length === 0) {
    levelComplete()
    return
  }
  
  // Check lose condition
  if (chainProgress.value >= 1) {
    gameOver()
  }
}

// Level complete
const levelComplete = () => {
  level.value++
  score.value += 1000 * level.value
  
  setTimeout(() => {
    initializeGame()
  }, 2000)
}

// Pause/resume game
const pauseGame = () => {
  isPaused.value = !isPaused.value
}

// Game over
const gameOver = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
  
  gameResult.value = {
    title: 'Game Over!',
    message: `Final Score: ${score.value} | Level: ${level.value}`
  }
}

// Reset game
const resetGame = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
  initializeGame()
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 600)
  boardSize.value = Math.max(400, maxSize)
  updateShooterPosition()
  createTrackPath()
}

onMounted(() => {
  initializeGame()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})

onUnmounted(() => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
  window.removeEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.zuma-board {
  position: relative;
  background: radial-gradient(circle, #1e293b, #0f172a);
  border: 3px solid #475569;
  border-radius: 50%;
  overflow: hidden;
  cursor: crosshair;
}

.track-path {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.marble {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid rgba(255,255,255,0.3);
  box-shadow: 
    inset 0 2px 4px rgba(255,255,255,0.4),
    0 2px 8px rgba(0,0,0,0.3);
  transition: all 0.1s ease;
}

.marble.small {
  width: 20px;
  height: 20px;
}

.marble.shooting {
  z-index: 1000;
}

.marble.matching {
  animation: matchPulse 0.3s ease-in-out;
}

.marble-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.marble-shine {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 30%;
  height: 30%;
  background: rgba(255,255,255,0.8);
  border-radius: 50%;
  filter: blur(1px);
}

.marble-symbol {
  font-size: 0.9em;
  z-index: 2;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
}

.shooter {
  position: absolute;
  z-index: 100;
  transform-origin: center;
}

.shooter-base {
  width: 30px;
  height: 30px;
  background: linear-gradient(to bottom, #4a5568, #2d3748);
  border-radius: 50%;
  position: relative;
}

.shooter-barrel {
  width: 40px;
  height: 8px;
  background: linear-gradient(to bottom, #718096, #4a5568);
  border-radius: 4px;
  position: absolute;
  top: 11px;
  left: 25px;
}

.current-marble {
  position: absolute;
  top: -15px;
  left: -15px;
}

.next-marble-preview {
  position: absolute;
  text-align: center;
  z-index: 100;
}

.aim-line {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 50;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

@keyframes matchPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); opacity: 0.7; }
}
</style>
