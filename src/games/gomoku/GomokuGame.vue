<template>
  <div class="gomoku-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.gomoku.name') }}</h2>
      <div class="flex justify-center items-center space-x-4">
        <span class="text-lg font-medium" :class="currentPlayer === 'black' ? 'text-gray-900' : 'text-gray-500'">
          ● Black
        </span>
        <span class="text-gray-400">vs</span>
        <span class="text-lg font-medium" :class="currentPlayer === 'white' ? 'text-gray-900' : 'text-gray-500'">
          ○ White
        </span>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Gomoku Board -->
    <div class="gomoku-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <svg :width="boardSize" :height="boardSize" class="board-svg">
        <!-- Grid lines -->
        <g class="grid-lines">
          <!-- Vertical lines -->
          <line 
            v-for="i in 15" 
            :key="'v' + i"
            :x1="cellSize * (i - 0.5)" 
            :y1="cellSize * 0.5" 
            :x2="cellSize * (i - 0.5)" 
            :y2="cellSize * 14.5"
            stroke="#8B4513" 
            stroke-width="1"
          />
          <!-- Horizontal lines -->
          <line 
            v-for="i in 15" 
            :key="'h' + i"
            :x1="cellSize * 0.5" 
            :y1="cellSize * (i - 0.5)" 
            :x2="cellSize * 14.5" 
            :y2="cellSize * (i - 0.5)"
            stroke="#8B4513" 
            stroke-width="1"
          />
        </g>
        
        <!-- Star points -->
        <g class="star-points">
          <circle cx="3.5" cy="3.5" r="3" fill="#8B4513" :transform="`scale(${cellSize / 30})`" />
          <circle cx="11.5" cy="3.5" r="3" fill="#8B4513" :transform="`scale(${cellSize / 30})`" />
          <circle cx="7.5" cy="7.5" r="3" fill="#8B4513" :transform="`scale(${cellSize / 30})`" />
          <circle cx="3.5" cy="11.5" r="3" fill="#8B4513" :transform="`scale(${cellSize / 30})`" />
          <circle cx="11.5" cy="11.5" r="3" fill="#8B4513" :transform="`scale(${cellSize / 30})`" />
        </g>
        
        <!-- Pieces -->
        <g class="pieces">
          <circle
            v-for="(piece, index) in placedPieces"
            :key="index"
            :cx="cellSize * piece.col"
            :cy="cellSize * piece.row"
            :r="cellSize * 0.4"
            :fill="piece.color === 'black' ? '#2D3748' : '#F7FAFC'"
            :stroke="piece.color === 'black' ? '#1A202C' : '#E2E8F0'"
            stroke-width="2"
            class="piece"
          />
        </g>
        
        <!-- Click areas -->
        <g class="click-areas">
          <template v-for="row in 15" :key="row">
            <circle
              v-for="col in 15"
              :key="`${row}-${col}`"
              :cx="cellSize * (col - 0.5)"
              :cy="cellSize * (row - 0.5)"
              :r="cellSize * 0.45"
              fill="transparent"
              class="click-area"
              @click="handleCellClick(row - 1, col - 1)"
            />
          </template>
        </g>
        
        <!-- Winning line -->
        <line
          v-if="winningLine"
          :x1="cellSize * winningLine.start.col"
          :y1="cellSize * winningLine.start.row"
          :x2="cellSize * winningLine.end.col"
          :y2="cellSize * winningLine.end.row"
          stroke="#FF6B6B"
          stroke-width="4"
          class="winning-line"
        />
      </svg>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        {{ $t('common.retry') }}
      </button>
      <button @click="undoMove" class="btn btn-secondary" :disabled="placedPieces.length === 0">
        Undo
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Types
interface Piece {
  row: number
  col: number
  color: 'black' | 'white'
}

interface WinningLine {
  start: { row: number; col: number }
  end: { row: number; col: number }
}

interface GameResult {
  title: string
  message: string
}

// Reactive state
const board = ref<(string | null)[][]>([])
const currentPlayer = ref<'black' | 'white'>('black')
const placedPieces = ref<Piece[]>([])
const gameResult = ref<GameResult | null>(null)
const winningLine = ref<WinningLine | null>(null)
const boardSize = ref(450)

// Computed
const cellSize = computed(() => boardSize.value / 15)

const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  return `${currentPlayer.value === 'black' ? 'Black' : 'White'} to move`
})

// Initialize board
const initializeBoard = () => {
  board.value = Array(15).fill(null).map(() => Array(15).fill(null))
  placedPieces.value = []
  currentPlayer.value = 'black'
  gameResult.value = null
  winningLine.value = null
}

// Handle cell click
const handleCellClick = (row: number, col: number) => {
  if (gameResult.value) return
  if (board.value[row][col]) return // Cell already occupied
  
  // Place piece
  board.value[row][col] = currentPlayer.value
  placedPieces.value.push({
    row: row + 0.5,
    col: col + 0.5,
    color: currentPlayer.value
  })
  
  // Check for win
  if (checkWin(row, col)) {
    gameResult.value = {
      title: `${currentPlayer.value === 'black' ? 'Black' : 'White'} Wins!`,
      message: `${currentPlayer.value === 'black' ? 'Black' : 'White'} got five in a row!`
    }
    return
  }
  
  // Check for draw
  if (placedPieces.value.length === 225) {
    gameResult.value = {
      title: 'Draw!',
      message: 'The board is full. It\'s a draw!'
    }
    return
  }
  
  // Switch player
  currentPlayer.value = currentPlayer.value === 'black' ? 'white' : 'black'
}

// Check for win
const checkWin = (row: number, col: number): boolean => {
  const color = board.value[row][col]
  const directions = [
    [0, 1],   // horizontal
    [1, 0],   // vertical
    [1, 1],   // diagonal \
    [1, -1]   // diagonal /
  ]
  
  for (const [dr, dc] of directions) {
    let count = 1
    let startRow = row
    let startCol = col
    let endRow = row
    let endCol = col
    
    // Check in positive direction
    for (let i = 1; i < 5; i++) {
      const newRow = row + dr * i
      const newCol = col + dc * i
      
      if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 && 
          board.value[newRow][newCol] === color) {
        count++
        endRow = newRow
        endCol = newCol
      } else {
        break
      }
    }
    
    // Check in negative direction
    for (let i = 1; i < 5; i++) {
      const newRow = row - dr * i
      const newCol = col - dc * i
      
      if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 && 
          board.value[newRow][newCol] === color) {
        count++
        startRow = newRow
        startCol = newCol
      } else {
        break
      }
    }
    
    if (count >= 5) {
      winningLine.value = {
        start: { row: startRow + 0.5, col: startCol + 0.5 },
        end: { row: endRow + 0.5, col: endCol + 0.5 }
      }
      return true
    }
  }
  
  return false
}

// Reset game
const resetGame = () => {
  initializeBoard()
}

// Undo move
const undoMove = () => {
  if (placedPieces.value.length === 0) return
  
  const lastPiece = placedPieces.value.pop()
  if (lastPiece) {
    const row = Math.floor(lastPiece.row)
    const col = Math.floor(lastPiece.col)
    board.value[row][col] = null
  }
  
  // Switch player back
  currentPlayer.value = currentPlayer.value === 'black' ? 'white' : 'black'
  
  // Clear game result
  gameResult.value = null
  winningLine.value = null
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 600)
  boardSize.value = Math.max(300, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.gomoku-board {
  background-color: #DEB887;
  border: 3px solid #8B4513;
  border-radius: 8px;
  position: relative;
}

.board-svg {
  display: block;
}

.click-area {
  cursor: pointer;
}

.click-area:hover {
  fill: rgba(0, 0, 0, 0.1);
}

.piece {
  transition: all 0.2s ease;
}

.winning-line {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@media (max-width: 640px) {
  .gomoku-board {
    border-width: 2px;
  }
}
</style>
