<template>
  <div class="jewels-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.jewels.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ moves }}</div>
          <div class="text-sm text-gray-500">Moves</div>
        </div>
      </div>
      <div class="mt-2">
        <div class="bg-gray-200 rounded-full h-2 w-64 mx-auto">
          <div 
            class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: (score / targetScore * 100) + '%' }"
          ></div>
        </div>
        <p class="text-sm text-gray-600 mt-1">{{ score }} / {{ targetScore }} points to next level</p>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="jewels-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <div 
        v-for="(row, rowIndex) in board" 
        :key="rowIndex"
        class="jewels-row"
      >
        <div
          v-for="(jewel, colIndex) in row"
          :key="colIndex"
          class="jewels-cell"
          :class="{
            'selected': selectedJewel && selectedJewel.row === rowIndex && selectedJewel.col === colIndex,
            'highlighted': isHighlighted(rowIndex, colIndex),
            'matching': jewel && jewel.matching,
            'falling': jewel && jewel.falling,
            'special': jewel && jewel.special
          }"
          @click="handleJewelClick(rowIndex, colIndex)"
        >
          <div v-if="jewel" class="jewel" :class="jewel.type">
            <div class="jewel-inner" :style="{ backgroundColor: getJewelColor(jewel.type) }">
              <div class="jewel-shine"></div>
              <div class="jewel-symbol">{{ getJewelSymbol(jewel.type) }}</div>
              <div v-if="jewel.special" class="special-effect">✨</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Power-ups -->
    <div class="mt-6 flex justify-center space-x-4">
      <button 
        @click="usePowerUp('bomb')" 
        class="power-up-btn"
        :disabled="powerUps.bomb <= 0"
        :class="{ 'disabled': powerUps.bomb <= 0 }"
      >
        💣 {{ powerUps.bomb }}
      </button>
      <button 
        @click="usePowerUp('lightning')" 
        class="power-up-btn"
        :disabled="powerUps.lightning <= 0"
        :class="{ 'disabled': powerUps.lightning <= 0 }"
      >
        ⚡ {{ powerUps.lightning }}
      </button>
      <button 
        @click="usePowerUp('rainbow')" 
        class="power-up-btn"
        :disabled="powerUps.rainbow <= 0"
        :class="{ 'disabled': powerUps.rainbow <= 0 }"
      >
        🌈 {{ powerUps.rainbow }}
      </button>
    </div>

    <!-- Game Controls -->
    <div class="mt-4 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="shuffleBoard" class="btn btn-primary" :disabled="!canShuffle">
        Shuffle
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'

// Types
interface Jewel {
  type: number
  falling?: boolean
  matching?: boolean
  special?: boolean
}

interface Position {
  row: number
  col: number
}

interface GameResult {
  title: string
  message: string
}

interface PowerUps {
  bomb: number
  lightning: number
  rainbow: number
}

// Constants
const BOARD_SIZE = 8
const JEWEL_TYPES = 6
const COLORS = ['#e11d48', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#f97316']
const SYMBOLS = ['💎', '💠', '🔷', '🔶', '🔸', '🔹']

// Reactive state
const board = ref<(Jewel | null)[][]>([])
const selectedJewel = ref<Position | null>(null)
const score = ref(0)
const level = ref(1)
const moves = ref(20)
const targetScore = ref(1000)
const gameResult = ref<GameResult | null>(null)
const boardSize = ref(400)
const highlightedCells = ref<Position[]>([])
const canShuffle = ref(true)
const powerUps = ref<PowerUps>({ bomb: 3, lightning: 2, rainbow: 1 })
const activePowerUp = ref<string | null>(null)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (moves.value <= 0) return 'No moves left!'
  if (activePowerUp.value) return `Click to use ${activePowerUp.value} power-up!`
  return 'Swap adjacent jewels to create matches of 3 or more!'
})

// Initialize board
const initializeBoard = () => {
  board.value = Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(null))
  
  // Fill board with random jewels, ensuring no initial matches
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE; col++) {
      let jewelType: number
      do {
        jewelType = Math.floor(Math.random() * JEWEL_TYPES)
      } while (wouldCreateMatch(row, col, jewelType))
      
      board.value[row][col] = { 
        type: jewelType,
        special: Math.random() < 0.05 // 5% chance for special jewel
      }
    }
  }
  
  selectedJewel.value = null
  score.value = 0
  level.value = 1
  moves.value = 20
  targetScore.value = 1000
  gameResult.value = null
  highlightedCells.value = []
  canShuffle.value = true
  powerUps.value = { bomb: 3, lightning: 2, rainbow: 1 }
  activePowerUp.value = null
}

// Check if placing a jewel would create a match
const wouldCreateMatch = (row: number, col: number, jewelType: number): boolean => {
  // Check horizontal
  let horizontalCount = 1
  // Check left
  for (let c = col - 1; c >= 0 && board.value[row][c]?.type === jewelType; c--) {
    horizontalCount++
  }
  // Check right
  for (let c = col + 1; c < BOARD_SIZE && board.value[row][c]?.type === jewelType; c++) {
    horizontalCount++
  }
  
  // Check vertical
  let verticalCount = 1
  // Check up
  for (let r = row - 1; r >= 0 && board.value[r][col]?.type === jewelType; r--) {
    verticalCount++
  }
  // Check down
  for (let r = row + 1; r < BOARD_SIZE && board.value[r][col]?.type === jewelType; r++) {
    verticalCount++
  }
  
  return horizontalCount >= 3 || verticalCount >= 3
}

// Handle jewel click
const handleJewelClick = (row: number, col: number) => {
  if (gameResult.value || moves.value <= 0) return
  
  // Handle power-up usage
  if (activePowerUp.value) {
    usePowerUpAt(row, col)
    return
  }
  
  const clickedPos = { row, col }
  
  if (!selectedJewel.value) {
    // First jewel selection
    selectedJewel.value = clickedPos
    highlightPossibleMoves(row, col)
  } else if (selectedJewel.value.row === row && selectedJewel.value.col === col) {
    // Deselect
    selectedJewel.value = null
    highlightedCells.value = []
  } else if (isAdjacent(selectedJewel.value, clickedPos)) {
    // Try to swap
    swapJewels(selectedJewel.value, clickedPos)
  } else {
    // Select new jewel
    selectedJewel.value = clickedPos
    highlightPossibleMoves(row, col)
  }
}

// Check if two positions are adjacent
const isAdjacent = (pos1: Position, pos2: Position): boolean => {
  const rowDiff = Math.abs(pos1.row - pos2.row)
  const colDiff = Math.abs(pos1.col - pos2.col)
  return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)
}

// Highlight possible moves
const highlightPossibleMoves = (row: number, col: number) => {
  highlightedCells.value = []
  const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
  
  for (const [dr, dc] of directions) {
    const newRow = row + dr
    const newCol = col + dc
    if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE) {
      highlightedCells.value.push({ row: newRow, col: newCol })
    }
  }
}

// Check if cell is highlighted
const isHighlighted = (row: number, col: number): boolean => {
  return highlightedCells.value.some(pos => pos.row === row && pos.col === col)
}

// Swap jewels
const swapJewels = async (pos1: Position, pos2: Position) => {
  const jewel1 = board.value[pos1.row][pos1.col]
  const jewel2 = board.value[pos2.row][pos2.col]
  
  // Perform swap
  board.value[pos1.row][pos1.col] = jewel2
  board.value[pos2.row][pos2.col] = jewel1
  
  // Check for matches
  const matches = findMatches()
  
  if (matches.length > 0) {
    // Valid move
    moves.value--
    selectedJewel.value = null
    highlightedCells.value = []
    
    await processMatches()
    
    // Check for level completion
    if (score.value >= targetScore.value) {
      levelUp()
    } else if (moves.value <= 0) {
      gameOver()
    }
  } else {
    // Invalid move, swap back
    board.value[pos1.row][pos1.col] = jewel1
    board.value[pos2.row][pos2.col] = jewel2
  }
}

// Find matches on the board
const findMatches = (): Position[] => {
  const matches: Position[] = []
  const visited = Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(false))
  
  // Check horizontal matches
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE - 2; col++) {
      const jewel = board.value[row][col]
      if (!jewel) continue
      
      let count = 1
      let endCol = col
      
      for (let c = col + 1; c < BOARD_SIZE && board.value[row][c]?.type === jewel.type; c++) {
        count++
        endCol = c
      }
      
      if (count >= 3) {
        for (let c = col; c <= endCol; c++) {
          if (!visited[row][c]) {
            matches.push({ row, col: c })
            visited[row][c] = true
          }
        }
      }
    }
  }
  
  // Check vertical matches
  for (let col = 0; col < BOARD_SIZE; col++) {
    for (let row = 0; row < BOARD_SIZE - 2; row++) {
      const jewel = board.value[row][col]
      if (!jewel) continue
      
      let count = 1
      let endRow = row
      
      for (let r = row + 1; r < BOARD_SIZE && board.value[r][col]?.type === jewel.type; r++) {
        count++
        endRow = r
      }
      
      if (count >= 3) {
        for (let r = row; r <= endRow; r++) {
          if (!visited[r][col]) {
            matches.push({ row: r, col })
            visited[r][col] = true
          }
        }
      }
    }
  }
  
  return matches
}

// Process matches and update score
const processMatches = async () => {
  let totalMatches = 0
  let combo = 0
  
  while (true) {
    const matches = findMatches()
    if (matches.length === 0) break
    
    // Mark matching jewels
    for (const match of matches) {
      const jewel = board.value[match.row][match.col]
      if (jewel) {
        jewel.matching = true
      }
    }
    
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Remove matched jewels and handle special effects
    for (const match of matches) {
      const jewel = board.value[match.row][match.col]
      if (jewel?.special) {
        // Special jewel creates explosion
        explodeArea(match.row, match.col)
      }
      board.value[match.row][match.col] = null
    }
    
    totalMatches += matches.length
    combo++
    
    // Apply gravity
    await applyGravity()
    
    // Fill empty spaces
    fillEmptySpaces()
    
    await nextTick()
  }
  
  // Update score with combo bonus
  if (totalMatches > 0) {
    const baseScore = totalMatches * 10
    const comboBonus = combo > 1 ? (combo - 1) * 50 : 0
    const levelBonus = level.value * 5
    score.value += baseScore + comboBonus + levelBonus
  }
}

// Explode area around special jewel
const explodeArea = (centerRow: number, centerCol: number) => {
  const radius = 1
  for (let row = Math.max(0, centerRow - radius); row <= Math.min(BOARD_SIZE - 1, centerRow + radius); row++) {
    for (let col = Math.max(0, centerCol - radius); col <= Math.min(BOARD_SIZE - 1, centerCol + radius); col++) {
      if (board.value[row][col]) {
        board.value[row][col] = null
      }
    }
  }
}

// Apply gravity to make jewels fall
const applyGravity = async () => {
  for (let col = 0; col < BOARD_SIZE; col++) {
    const column = []
    
    // Collect non-null jewels
    for (let row = BOARD_SIZE - 1; row >= 0; row--) {
      if (board.value[row][col]) {
        column.push(board.value[row][col])
      }
    }
    
    // Clear column
    for (let row = 0; row < BOARD_SIZE; row++) {
      board.value[row][col] = null
    }
    
    // Place jewels at bottom
    for (let i = 0; i < column.length; i++) {
      const jewel = column[i]!
      jewel.falling = true
      jewel.matching = false
      board.value[BOARD_SIZE - 1 - i][col] = jewel
    }
  }
  
  // Remove falling flag after animation
  setTimeout(() => {
    for (let row = 0; row < BOARD_SIZE; row++) {
      for (let col = 0; col < BOARD_SIZE; col++) {
        if (board.value[row][col]) {
          board.value[row][col]!.falling = false
        }
      }
    }
  }, 300)
}

// Fill empty spaces with new jewels
const fillEmptySpaces = () => {
  for (let col = 0; col < BOARD_SIZE; col++) {
    for (let row = 0; row < BOARD_SIZE; row++) {
      if (!board.value[row][col]) {
        board.value[row][col] = {
          type: Math.floor(Math.random() * JEWEL_TYPES),
          falling: true,
          special: Math.random() < 0.03 // 3% chance for special jewel
        }
      }
    }
  }
}

// Use power-up
const usePowerUp = (type: string) => {
  if (powerUps.value[type as keyof PowerUps] <= 0) return
  
  activePowerUp.value = type
  selectedJewel.value = null
  highlightedCells.value = []
}

// Use power-up at position
const usePowerUpAt = async (row: number, col: number) => {
  if (!activePowerUp.value) return
  
  const type = activePowerUp.value
  powerUps.value[type as keyof PowerUps]--
  activePowerUp.value = null
  
  switch (type) {
    case 'bomb':
      // Explode 3x3 area
      for (let r = Math.max(0, row - 1); r <= Math.min(BOARD_SIZE - 1, row + 1); r++) {
        for (let c = Math.max(0, col - 1); c <= Math.min(BOARD_SIZE - 1, col + 1); c++) {
          board.value[r][c] = null
        }
      }
      break
    case 'lightning':
      // Clear entire row and column
      for (let c = 0; c < BOARD_SIZE; c++) {
        board.value[row][c] = null
      }
      for (let r = 0; r < BOARD_SIZE; r++) {
        board.value[r][col] = null
      }
      break
    case 'rainbow':
      // Clear all jewels of the same type
      const targetType = board.value[row][col]?.type
      if (targetType !== undefined) {
        for (let r = 0; r < BOARD_SIZE; r++) {
          for (let c = 0; c < BOARD_SIZE; c++) {
            if (board.value[r][c]?.type === targetType) {
              board.value[r][c] = null
            }
          }
        }
      }
      break
  }
  
  await applyGravity()
  fillEmptySpaces()
  await processMatches()
}

// Shuffle board
const shuffleBoard = () => {
  if (!canShuffle.value) return
  
  const jewels: Jewel[] = []
  
  // Collect all jewels
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE; col++) {
      if (board.value[row][col]) {
        jewels.push(board.value[row][col]!)
      }
    }
  }
  
  // Shuffle jewels
  for (let i = jewels.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[jewels[i], jewels[j]] = [jewels[j], jewels[i]]
  }
  
  // Place shuffled jewels back
  let jewelIndex = 0
  for (let row = 0; row < BOARD_SIZE; row++) {
    for (let col = 0; col < BOARD_SIZE; col++) {
      board.value[row][col] = jewels[jewelIndex++] || null
    }
  }
  
  selectedJewel.value = null
  highlightedCells.value = []
  canShuffle.value = false
  
  // Re-enable shuffle after some moves
  setTimeout(() => {
    canShuffle.value = true
  }, 5000)
}

// Level up
const levelUp = () => {
  level.value++
  moves.value += 15
  targetScore.value = Math.floor(targetScore.value * 1.5)
  
  // Bonus power-ups
  powerUps.value.bomb += 1
  if (level.value % 3 === 0) {
    powerUps.value.lightning += 1
  }
  if (level.value % 5 === 0) {
    powerUps.value.rainbow += 1
  }
}

// Game over
const gameOver = () => {
  gameResult.value = {
    title: 'Game Over!',
    message: `Final Score: ${score.value} | Level: ${level.value}`
  }
}

// Get jewel color
const getJewelColor = (type: number): string => {
  return COLORS[type] || '#gray'
}

// Get jewel symbol
const getJewelSymbol = (type: number): string => {
  return SYMBOLS[type] || '💎'
}

// Reset game
const resetGame = () => {
  initializeBoard()
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 500)
  boardSize.value = Math.max(300, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.jewels-board {
  display: grid;
  grid-template-rows: repeat(8, 1fr);
  gap: 2px;
  background: linear-gradient(135deg, #1e293b, #334155);
  border: 3px solid #475569;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.jewels-row {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 2px;
}

.jewels-cell {
  aspect-ratio: 1;
  background: radial-gradient(circle, #374151, #1f2937);
  border: 1px solid #4b5563;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.jewels-cell:hover {
  background: radial-gradient(circle, #4b5563, #374151);
  transform: scale(1.02);
}

.jewels-cell.selected {
  background: radial-gradient(circle, #3b82f6, #1d4ed8);
  box-shadow: 0 0 0 2px #60a5fa;
  transform: scale(1.05);
}

.jewels-cell.highlighted {
  background: radial-gradient(circle, #10b981, #047857);
  box-shadow: 0 0 0 1px #34d399;
}

.jewels-cell.matching .jewel {
  animation: matchPulse 0.3s ease-in-out;
}

.jewels-cell.falling .jewel {
  animation: fall 0.3s ease-out;
}

.jewels-cell.special {
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
}

.jewel {
  width: 90%;
  height: 90%;
  position: relative;
}

.jewel-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255,255,255,0.3);
  box-shadow: 
    inset 0 2px 4px rgba(255,255,255,0.4),
    0 4px 12px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.jewel-shine {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 30%;
  height: 30%;
  background: rgba(255,255,255,0.8);
  border-radius: 50%;
  filter: blur(2px);
}

.jewel-symbol {
  font-size: 1.2em;
  z-index: 2;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
}

.special-effect {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.8em;
  animation: sparkle 1s infinite;
}

.power-up-btn {
  @apply px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg font-medium transition-all;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.4);
}

.power-up-btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(168, 85, 247, 0.6);
}

.power-up-btn.disabled {
  @apply bg-gray-400 cursor-not-allowed;
  box-shadow: none;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

@keyframes matchPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes fall {
  from {
    transform: translateY(-100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
}
</style>
