<template>
  <div class="checkers-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.checkers.name') }}</h2>
      <div class="flex justify-center items-center space-x-4">
        <span class="text-lg font-medium" :class="currentPlayer === 'red' ? 'text-red-600' : 'text-gray-500'">
          🔴 Red
        </span>
        <span class="text-gray-400">vs</span>
        <span class="text-lg font-medium" :class="currentPlayer === 'black' ? 'text-gray-900' : 'text-gray-500'">
          ⚫ Black
        </span>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Checkers Board -->
    <div class="checkers-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <div 
        v-for="(row, rowIndex) in board" 
        :key="rowIndex"
        class="checkers-row"
      >
        <div
          v-for="(cell, colIndex) in row"
          :key="colIndex"
          class="checkers-cell"
          :class="{
            'light': (rowIndex + colIndex) % 2 === 0,
            'dark': (rowIndex + colIndex) % 2 === 1,
            'selected': selectedSquare && selectedSquare.row === rowIndex && selectedSquare.col === colIndex,
            'possible-move': isPossibleMove(rowIndex, colIndex),
            'must-capture': mustCaptureSquares.some(sq => sq.row === rowIndex && sq.col === colIndex)
          }"
          @click="handleSquareClick(rowIndex, colIndex)"
        >
          <!-- Piece -->
          <div v-if="cell" class="checkers-piece" :class="[cell.color, { 'king': cell.isKing }]">
            <div class="piece-inner">
              {{ cell.isKing ? (cell.color === 'red' ? '♔' : '♚') : (cell.color === 'red' ? '●' : '●') }}
            </div>
          </div>
          
          <!-- Possible move indicator -->
          <div v-if="isPossibleMove(rowIndex, colIndex)" class="move-indicator"></div>
        </div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        {{ $t('common.retry') }}
      </button>
      <button @click="undoMove" class="btn btn-secondary" :disabled="moveHistory.length === 0">
        Undo
      </button>
    </div>

    <!-- Game Info -->
    <div class="mt-6 grid grid-cols-2 gap-4 max-w-md mx-auto">
      <div class="bg-white rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-red-600">{{ redPieces }}</div>
        <div class="text-sm text-gray-600">Red Pieces</div>
      </div>
      <div class="bg-white rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-gray-900">{{ blackPieces }}</div>
        <div class="text-sm text-gray-600">Black Pieces</div>
      </div>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Types
interface Piece {
  color: 'red' | 'black'
  isKing: boolean
}

interface Square {
  row: number
  col: number
}

interface Move {
  from: Square
  to: Square
  captured?: Square[]
}

interface GameResult {
  title: string
  message: string
}

// Reactive state
const board = ref<(Piece | null)[][]>([])
const currentPlayer = ref<'red' | 'black'>('red')
const selectedSquare = ref<Square | null>(null)
const possibleMoves = ref<Square[]>([])
const mustCaptureSquares = ref<Square[]>([])
const moveHistory = ref<Move[]>([])
const gameResult = ref<GameResult | null>(null)
const boardSize = ref(400)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (mustCaptureSquares.value.length > 0) {
    return `${currentPlayer.value === 'red' ? 'Red' : 'Black'} must capture!`
  }
  return `${currentPlayer.value === 'red' ? 'Red' : 'Black'} to move`
})

const redPieces = computed(() => {
  return board.value.flat().filter(piece => piece && piece.color === 'red').length
})

const blackPieces = computed(() => {
  return board.value.flat().filter(piece => piece && piece.color === 'black').length
})

// Initialize board
const initializeBoard = () => {
  const newBoard: (Piece | null)[][] = Array(8).fill(null).map(() => Array(8).fill(null))
  
  // Place red pieces (bottom)
  for (let row = 5; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      if ((row + col) % 2 === 1) {
        newBoard[row][col] = { color: 'red', isKing: false }
      }
    }
  }
  
  // Place black pieces (top)
  for (let row = 0; row < 3; row++) {
    for (let col = 0; col < 8; col++) {
      if ((row + col) % 2 === 1) {
        newBoard[row][col] = { color: 'black', isKing: false }
      }
    }
  }
  
  board.value = newBoard
  currentPlayer.value = 'red'
  selectedSquare.value = null
  possibleMoves.value = []
  mustCaptureSquares.value = []
  moveHistory.value = []
  gameResult.value = null
  
  updateMustCaptureSquares()
}

// Check if square is a possible move
const isPossibleMove = (row: number, col: number): boolean => {
  return possibleMoves.value.some(move => move.row === row && move.col === col)
}

// Update squares that must capture
const updateMustCaptureSquares = () => {
  const captures: Square[] = []
  
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board.value[row][col]
      if (piece && piece.color === currentPlayer.value) {
        const captureMoves = getCaptureMoves(row, col)
        if (captureMoves.length > 0) {
          captures.push({ row, col })
        }
      }
    }
  }
  
  mustCaptureSquares.value = captures
}

// Get capture moves for a piece
const getCaptureMoves = (row: number, col: number): Square[] => {
  const piece = board.value[row][col]
  if (!piece) return []
  
  const captures: Square[] = []
  const directions = piece.isKing 
    ? [[-1, -1], [-1, 1], [1, -1], [1, 1]] 
    : piece.color === 'red' 
      ? [[-1, -1], [-1, 1]] 
      : [[1, -1], [1, 1]]
  
  for (const [dr, dc] of directions) {
    const jumpRow = row + dr * 2
    const jumpCol = col + dc * 2
    const captureRow = row + dr
    const captureCol = col + dc
    
    if (jumpRow >= 0 && jumpRow < 8 && jumpCol >= 0 && jumpCol < 8) {
      const capturedPiece = board.value[captureRow][captureCol]
      const jumpSquare = board.value[jumpRow][jumpCol]
      
      if (capturedPiece && capturedPiece.color !== piece.color && !jumpSquare) {
        captures.push({ row: jumpRow, col: jumpCol })
      }
    }
  }
  
  return captures
}

// Get regular moves for a piece
const getRegularMoves = (row: number, col: number): Square[] => {
  const piece = board.value[row][col]
  if (!piece) return []
  
  const moves: Square[] = []
  const directions = piece.isKing 
    ? [[-1, -1], [-1, 1], [1, -1], [1, 1]] 
    : piece.color === 'red' 
      ? [[-1, -1], [-1, 1]] 
      : [[1, -1], [1, 1]]
  
  for (const [dr, dc] of directions) {
    const newRow = row + dr
    const newCol = col + dc
    
    if (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
      if (!board.value[newRow][newCol]) {
        moves.push({ row: newRow, col: newCol })
      }
    }
  }
  
  return moves
}

// Handle square click
const handleSquareClick = (row: number, col: number) => {
  const piece = board.value[row][col]
  
  if (selectedSquare.value) {
    // Try to make a move
    if (isPossibleMove(row, col)) {
      makeMove(selectedSquare.value, { row, col })
    }
    
    // Deselect
    selectedSquare.value = null
    possibleMoves.value = []
  } else if (piece && piece.color === currentPlayer.value) {
    // Check if this piece must capture
    if (mustCaptureSquares.value.length > 0) {
      const mustCapture = mustCaptureSquares.value.some(sq => sq.row === row && sq.col === col)
      if (!mustCapture) return
    }
    
    // Select piece
    selectedSquare.value = { row, col }
    
    // Get possible moves
    if (mustCaptureSquares.value.length > 0) {
      possibleMoves.value = getCaptureMoves(row, col)
    } else {
      possibleMoves.value = getRegularMoves(row, col)
    }
  }
}

// Make a move
const makeMove = (from: Square, to: Square) => {
  const piece = board.value[from.row][from.col]
  if (!piece) return
  
  const captured: Square[] = []
  
  // Check if it's a capture move
  const rowDiff = Math.abs(to.row - from.row)
  if (rowDiff === 2) {
    const captureRow = (from.row + to.row) / 2
    const captureCol = (from.col + to.col) / 2
    captured.push({ row: captureRow, col: captureCol })
    board.value[captureRow][captureCol] = null
  }
  
  // Move piece
  board.value[to.row][to.col] = { ...piece }
  board.value[from.row][from.col] = null
  
  // Check for king promotion
  if ((piece.color === 'red' && to.row === 0) || (piece.color === 'black' && to.row === 7)) {
    board.value[to.row][to.col]!.isKing = true
  }
  
  // Record move
  moveHistory.value.push({ from, to, captured })
  
  // Check for additional captures
  if (captured.length > 0) {
    const additionalCaptures = getCaptureMoves(to.row, to.col)
    if (additionalCaptures.length > 0) {
      // Continue with same player for multiple captures
      selectedSquare.value = to
      possibleMoves.value = additionalCaptures
      mustCaptureSquares.value = [to]
      return
    }
  }
  
  // Switch player
  currentPlayer.value = currentPlayer.value === 'red' ? 'black' : 'red'
  updateMustCaptureSquares()
  
  // Check for game end
  checkGameEnd()
}

// Check for game end
const checkGameEnd = () => {
  const currentPlayerPieces = board.value.flat().filter(piece => piece && piece.color === currentPlayer.value)
  
  if (currentPlayerPieces.length === 0) {
    gameResult.value = {
      title: `${currentPlayer.value === 'red' ? 'Black' : 'Red'} Wins!`,
      message: `${currentPlayer.value} has no pieces left!`
    }
    return
  }
  
  // Check if current player has any valid moves
  let hasValidMoves = false
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board.value[row][col]
      if (piece && piece.color === currentPlayer.value) {
        const captures = getCaptureMoves(row, col)
        const regular = getRegularMoves(row, col)
        if (captures.length > 0 || (mustCaptureSquares.value.length === 0 && regular.length > 0)) {
          hasValidMoves = true
          break
        }
      }
    }
    if (hasValidMoves) break
  }
  
  if (!hasValidMoves) {
    gameResult.value = {
      title: `${currentPlayer.value === 'red' ? 'Black' : 'Red'} Wins!`,
      message: `${currentPlayer.value} has no valid moves!`
    }
  }
}

// Reset game
const resetGame = () => {
  initializeBoard()
}

// Undo move
const undoMove = () => {
  if (moveHistory.value.length === 0) return
  
  const lastMove = moveHistory.value.pop()!
  
  // Restore piece
  const piece = board.value[lastMove.to.row][lastMove.to.col]
  board.value[lastMove.from.row][lastMove.from.col] = piece
  board.value[lastMove.to.row][lastMove.to.col] = null
  
  // Restore captured pieces
  for (const captured of lastMove.captured || []) {
    board.value[captured.row][captured.col] = { 
      color: currentPlayer.value === 'red' ? 'black' : 'red', 
      isKing: false 
    }
  }
  
  // Switch player back
  currentPlayer.value = currentPlayer.value === 'red' ? 'black' : 'red'
  
  // Clear selection
  selectedSquare.value = null
  possibleMoves.value = []
  gameResult.value = null
  
  updateMustCaptureSquares()
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 600)
  boardSize.value = Math.max(320, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.checkers-board {
  border: 2px solid #8B4513;
  position: relative;
  margin: 0 auto;
}

.checkers-row {
  display: flex;
  height: 12.5%;
}

.checkers-cell {
  width: 12.5%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkers-cell.light {
  background-color: #f0d9b5;
}

.checkers-cell.dark {
  background-color: #b58863;
}

.checkers-cell.selected {
  background-color: #ffff00 !important;
  box-shadow: inset 0 0 0 3px #ff6b6b;
}

.checkers-cell.possible-move {
  background-color: #90EE90 !important;
}

.checkers-cell.must-capture {
  background-color: #ffcccb !important;
  animation: pulse 1s infinite;
}

.checkers-piece {
  width: 80%;
  height: 80%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  transition: transform 0.1s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.checkers-piece:hover {
  transform: scale(1.05);
}

.checkers-piece.red {
  background: linear-gradient(145deg, #ff6b6b, #e55555);
  color: white;
}

.checkers-piece.black {
  background: linear-gradient(145deg, #4a4a4a, #2d2d2d);
  color: white;
}

.checkers-piece.king {
  border: 3px solid gold;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.piece-inner {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.move-indicator {
  width: 20%;
  height: 20%;
  background-color: rgba(0, 128, 0, 0.6);
  border-radius: 50%;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@media (max-width: 640px) {
  .checkers-piece {
    font-size: 1.2rem;
  }
}
</style>
