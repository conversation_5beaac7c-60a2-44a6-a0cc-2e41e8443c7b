<template>
  <div class="reversi-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.reversi.name') }}</h2>
      <div class="flex justify-center items-center space-x-4">
        <span class="text-lg font-medium" :class="currentPlayer === 'black' ? 'text-gray-900' : 'text-gray-500'">
          ⚫ Black
        </span>
        <span class="text-gray-400">vs</span>
        <span class="text-lg font-medium" :class="currentPlayer === 'white' ? 'text-gray-900' : 'text-gray-500'">
          ⚪ White
        </span>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Reversi Board -->
    <div class="reversi-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <div 
        v-for="(row, rowIndex) in board" 
        :key="rowIndex"
        class="reversi-row"
      >
        <div
          v-for="(cell, colIndex) in row"
          :key="colIndex"
          class="reversi-cell"
          :class="{
            'possible-move': isPossibleMove(rowIndex, colIndex),
            'last-move': isLastMove(rowIndex, colIndex)
          }"
          @click="handleCellClick(rowIndex, colIndex)"
        >
          <!-- Piece -->
          <div v-if="cell" class="reversi-piece" :class="cell">
            <div class="piece-inner"></div>
          </div>
          
          <!-- Possible move indicator -->
          <div v-else-if="isPossibleMove(rowIndex, colIndex)" class="move-indicator">
            <div class="preview-piece" :class="currentPlayer"></div>
          </div>
          
          <!-- Grid lines -->
          <div class="grid-lines"></div>
        </div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        {{ $t('common.retry') }}
      </button>
      <button @click="undoMove" class="btn btn-secondary" :disabled="moveHistory.length === 0">
        Undo
      </button>
      <button @click="passMove" class="btn btn-secondary" :disabled="possibleMoves.length > 0">
        Pass
      </button>
    </div>

    <!-- Score Display -->
    <div class="mt-6 grid grid-cols-2 gap-4 max-w-md mx-auto">
      <div class="bg-white rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-gray-900">{{ blackCount }}</div>
        <div class="text-sm text-gray-600">⚫ Black</div>
      </div>
      <div class="bg-white rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-gray-600">{{ whiteCount }}</div>
        <div class="text-sm text-gray-600">⚪ White</div>
      </div>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-2">{{ gameResult.message }}</p>
        <p class="text-sm text-gray-500 mb-4">Final Score: Black {{ blackCount }} - White {{ whiteCount }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Types
interface Square {
  row: number
  col: number
}

interface Move {
  square: Square
  flipped: Square[]
  player: 'black' | 'white'
}

interface GameResult {
  title: string
  message: string
}

// Reactive state
const board = ref<(string | null)[][]>([])
const currentPlayer = ref<'black' | 'white'>('black')
const possibleMoves = ref<Square[]>([])
const moveHistory = ref<Move[]>([])
const lastMove = ref<Square | null>(null)
const gameResult = ref<GameResult | null>(null)
const boardSize = ref(400)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (possibleMoves.value.length === 0) {
    return `${currentPlayer.value === 'black' ? 'Black' : 'White'} must pass`
  }
  return `${currentPlayer.value === 'black' ? 'Black' : 'White'} to move`
})

const blackCount = computed(() => {
  return board.value.flat().filter(cell => cell === 'black').length
})

const whiteCount = computed(() => {
  return board.value.flat().filter(cell => cell === 'white').length
})

// Initialize board
const initializeBoard = () => {
  // Create 8x8 empty board
  board.value = Array(8).fill(null).map(() => Array(8).fill(null))
  
  // Place initial pieces
  board.value[3][3] = 'white'
  board.value[3][4] = 'black'
  board.value[4][3] = 'black'
  board.value[4][4] = 'white'
  
  currentPlayer.value = 'black'
  moveHistory.value = []
  lastMove.value = null
  gameResult.value = null
  
  updatePossibleMoves()
}

// Check if square is a possible move
const isPossibleMove = (row: number, col: number): boolean => {
  return possibleMoves.value.some(move => move.row === row && move.col === col)
}

// Check if square is the last move
const isLastMove = (row: number, col: number): boolean => {
  return lastMove.value?.row === row && lastMove.value?.col === col
}

// Get pieces that would be flipped by a move
const getFlippedPieces = (row: number, col: number, player: 'black' | 'white'): Square[] => {
  if (board.value[row][col] !== null) return []
  
  const opponent = player === 'black' ? 'white' : 'black'
  const flipped: Square[] = []
  const directions = [
    [-1, -1], [-1, 0], [-1, 1],
    [0, -1],           [0, 1],
    [1, -1],  [1, 0],  [1, 1]
  ]
  
  for (const [dr, dc] of directions) {
    const lineFlipped: Square[] = []
    let r = row + dr
    let c = col + dc
    
    // Look for opponent pieces
    while (r >= 0 && r < 8 && c >= 0 && c < 8 && board.value[r][c] === opponent) {
      lineFlipped.push({ row: r, col: c })
      r += dr
      c += dc
    }
    
    // If we found opponent pieces and ended with our piece, add to flipped
    if (lineFlipped.length > 0 && r >= 0 && r < 8 && c >= 0 && c < 8 && board.value[r][c] === player) {
      flipped.push(...lineFlipped)
    }
  }
  
  return flipped
}

// Update possible moves for current player
const updatePossibleMoves = () => {
  const moves: Square[] = []
  
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      if (board.value[row][col] === null) {
        const flipped = getFlippedPieces(row, col, currentPlayer.value)
        if (flipped.length > 0) {
          moves.push({ row, col })
        }
      }
    }
  }
  
  possibleMoves.value = moves
}

// Handle cell click
const handleCellClick = (row: number, col: number) => {
  if (gameResult.value) return
  if (!isPossibleMove(row, col)) return
  
  makeMove(row, col)
}

// Make a move
const makeMove = (row: number, col: number) => {
  const flipped = getFlippedPieces(row, col, currentPlayer.value)
  if (flipped.length === 0) return
  
  // Place piece
  board.value[row][col] = currentPlayer.value
  
  // Flip pieces
  for (const square of flipped) {
    board.value[square.row][square.col] = currentPlayer.value
  }
  
  // Record move
  moveHistory.value.push({
    square: { row, col },
    flipped,
    player: currentPlayer.value
  })
  
  lastMove.value = { row, col }
  
  // Switch player
  currentPlayer.value = currentPlayer.value === 'black' ? 'white' : 'black'
  updatePossibleMoves()
  
  // Check if opponent has moves
  if (possibleMoves.value.length === 0) {
    // Switch back and check if current player has moves
    currentPlayer.value = currentPlayer.value === 'black' ? 'white' : 'black'
    updatePossibleMoves()
    
    if (possibleMoves.value.length === 0) {
      // Game over
      endGame()
    }
  }
}

// Pass move
const passMove = () => {
  if (possibleMoves.value.length > 0) return
  
  currentPlayer.value = currentPlayer.value === 'black' ? 'white' : 'black'
  updatePossibleMoves()
  
  if (possibleMoves.value.length === 0) {
    endGame()
  }
}

// End game
const endGame = () => {
  const black = blackCount.value
  const white = whiteCount.value
  
  if (black > white) {
    gameResult.value = {
      title: 'Black Wins!',
      message: `Black wins with ${black} pieces!`
    }
  } else if (white > black) {
    gameResult.value = {
      title: 'White Wins!',
      message: `White wins with ${white} pieces!`
    }
  } else {
    gameResult.value = {
      title: 'Draw!',
      message: 'The game is a tie!'
    }
  }
}

// Reset game
const resetGame = () => {
  initializeBoard()
}

// Undo move
const undoMove = () => {
  if (moveHistory.value.length === 0) return
  
  const lastMove = moveHistory.value.pop()!
  
  // Remove placed piece
  board.value[lastMove.square.row][lastMove.square.col] = null
  
  // Unflip pieces
  const opponent = lastMove.player === 'black' ? 'white' : 'black'
  for (const square of lastMove.flipped) {
    board.value[square.row][square.col] = opponent
  }
  
  // Switch player back
  currentPlayer.value = lastMove.player
  
  // Clear game result
  gameResult.value = null
  lastMove.value = moveHistory.value.length > 0 ? moveHistory.value[moveHistory.value.length - 1].square : null
  
  updatePossibleMoves()
}

// Responsive board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 600)
  boardSize.value = Math.max(320, maxSize)
}

onMounted(() => {
  initializeBoard()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.reversi-board {
  background-color: #2d5a27;
  border: 3px solid #1a3d1a;
  position: relative;
  margin: 0 auto;
  border-radius: 8px;
}

.reversi-row {
  display: flex;
  height: 12.5%;
}

.reversi-cell {
  width: 12.5%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #1a3d1a;
}

.reversi-cell:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.reversi-cell.possible-move {
  background-color: rgba(144, 238, 144, 0.3);
}

.reversi-cell.last-move {
  background-color: rgba(255, 235, 59, 0.3);
}

.reversi-piece {
  width: 85%;
  height: 85%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.reversi-piece:hover {
  transform: scale(1.05);
}

.reversi-piece.black {
  background: radial-gradient(circle at 30% 30%, #4a4a4a, #1a1a1a);
}

.reversi-piece.white {
  background: radial-gradient(circle at 30% 30%, #ffffff, #e0e0e0);
}

.piece-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.2);
}

.move-indicator {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-piece {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.preview-piece.black {
  background: radial-gradient(circle at 30% 30%, #4a4a4a, #1a1a1a);
}

.preview-piece.white {
  background: radial-gradient(circle at 30% 30%, #ffffff, #e0e0e0);
}

.reversi-cell:hover .preview-piece {
  opacity: 0.8;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

@media (max-width: 640px) {
  .reversi-board {
    border-width: 2px;
  }
  
  .reversi-cell {
    border-width: 0.5px;
  }
}
</style>
