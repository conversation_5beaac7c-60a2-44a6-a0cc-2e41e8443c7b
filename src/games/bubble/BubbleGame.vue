<template>
  <div class="bubble-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.bubble.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-red-600">{{ bubblesLeft }}</div>
          <div class="text-sm text-gray-500">Bubbles</div>
        </div>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="bubble-board mx-auto" :style="{ width: boardWidth + 'px', height: boardHeight + 'px' }">
      <!-- Background -->
      <div class="board-background"></div>
      
      <!-- Bubbles -->
      <div
        v-for="(bubble, index) in bubbles"
        :key="index"
        class="bubble"
        :class="bubble.color"
        :style="{
          left: bubble.x + 'px',
          top: bubble.y + 'px',
          backgroundColor: getBubbleColor(bubble.color)
        }"
      >
        <div class="bubble-inner"></div>
      </div>
      
      <!-- Shooting bubble -->
      <div
        v-if="shootingBubble"
        class="bubble shooting"
        :class="shootingBubble.color"
        :style="{
          left: shootingBubble.x + 'px',
          top: shootingBubble.y + 'px',
          backgroundColor: getBubbleColor(shootingBubble.color)
        }"
      >
        <div class="bubble-inner"></div>
      </div>
      
      <!-- Aim line -->
      <svg v-if="aimLine" class="aim-line" :width="boardWidth" :height="boardHeight">
        <line
          :x1="aimLine.startX"
          :y1="aimLine.startY"
          :x2="aimLine.endX"
          :y2="aimLine.endY"
          stroke="#3b82f6"
          stroke-width="2"
          stroke-dasharray="5,5"
          opacity="0.7"
        />
      </svg>
      
      <!-- Shooter -->
      <div class="shooter" :style="{ left: shooterX + 'px', bottom: '20px' }">
        <div class="shooter-base"></div>
        <div
          class="current-bubble bubble"
          :class="currentBubble?.color"
          :style="{ backgroundColor: getBubbleColor(currentBubble?.color || 'red') }"
        >
          <div class="bubble-inner"></div>
        </div>
      </div>
      
      <!-- Next bubble preview -->
      <div class="next-bubble-preview" :style="{ left: (shooterX + 60) + 'px', bottom: '20px' }">
        <div class="text-xs text-gray-500 mb-1">Next</div>
        <div
          class="bubble small"
          :class="nextBubble?.color"
          :style="{ backgroundColor: getBubbleColor(nextBubble?.color || 'blue') }"
        >
          <div class="bubble-inner"></div>
        </div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="pauseGame" class="btn btn-primary" v-if="!gameResult">
        {{ isPaused ? 'Resume' : 'Pause' }}
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// Types
interface Bubble {
  x: number
  y: number
  color: string
  row: number
  col: number
}

interface ShootingBubble {
  x: number
  y: number
  color: string
  vx: number
  vy: number
}

interface AimLine {
  startX: number
  startY: number
  endX: number
  endY: number
}

interface GameResult {
  title: string
  message: string
}

// Constants
const BUBBLE_SIZE = 30
const BUBBLE_COLORS = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']
const COLOR_MAP = {
  red: '#ef4444',
  blue: '#3b82f6',
  green: '#10b981',
  yellow: '#f59e0b',
  purple: '#8b5cf6',
  orange: '#f97316'
}
const ROWS = 12
const COLS = 15
const SHOOTING_SPEED = 8

// Reactive state
const bubbles = ref<Bubble[]>([])
const shootingBubble = ref<ShootingBubble | null>(null)
const currentBubble = ref<{ color: string } | null>(null)
const nextBubble = ref<{ color: string } | null>(null)
const score = ref(0)
const level = ref(1)
const gameResult = ref<GameResult | null>(null)
const isPaused = ref(false)
const boardWidth = ref(450)
const boardHeight = ref(600)
const shooterX = ref(210)
const aimLine = ref<AimLine | null>(null)
const mouseX = ref(0)
const mouseY = ref(0)
const animationId = ref<number | null>(null)

// Computed
const bubblesLeft = computed(() => bubbles.value.length)

const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (isPaused.value) return 'Game Paused'
  return 'Aim and shoot to match 3 or more bubbles!'
})

// Initialize game
const initializeGame = () => {
  bubbles.value = []
  score.value = 0
  level.value = 1
  gameResult.value = null
  isPaused.value = false
  shootingBubble.value = null
  
  // Create initial bubble layout
  createInitialBubbles()
  
  // Set up shooter
  currentBubble.value = { color: getRandomColor() }
  nextBubble.value = { color: getRandomColor() }
  
  updateShooterPosition()
}

// Create initial bubble layout
const createInitialBubbles = () => {
  const initialRows = 8 + level.value
  
  for (let row = 0; row < Math.min(initialRows, ROWS - 2); row++) {
    const colsInRow = row % 2 === 0 ? COLS : COLS - 1
    const offsetX = row % 2 === 0 ? 0 : BUBBLE_SIZE / 2
    
    for (let col = 0; col < colsInRow; col++) {
      const x = col * BUBBLE_SIZE + offsetX + BUBBLE_SIZE / 2
      const y = row * (BUBBLE_SIZE * 0.87) + BUBBLE_SIZE / 2
      
      bubbles.value.push({
        x,
        y,
        color: getRandomColor(),
        row,
        col
      })
    }
  }
}

// Get random bubble color
const getRandomColor = (): string => {
  return BUBBLE_COLORS[Math.floor(Math.random() * BUBBLE_COLORS.length)]
}

// Get bubble color
const getBubbleColor = (color: string): string => {
  return COLOR_MAP[color as keyof typeof COLOR_MAP] || '#gray'
}

// Update shooter position
const updateShooterPosition = () => {
  shooterX.value = boardWidth.value / 2 - BUBBLE_SIZE / 2
}

// Handle mouse move for aiming
const handleMouseMove = (event: MouseEvent) => {
  if (gameResult.value || isPaused.value || shootingBubble.value) return
  
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  mouseX.value = event.clientX - rect.left
  mouseY.value = event.clientY - rect.top
  
  updateAimLine()
}

// Update aim line
const updateAimLine = () => {
  const startX = shooterX.value + BUBBLE_SIZE / 2
  const startY = boardHeight.value - 40
  
  const dx = mouseX.value - startX
  const dy = mouseY.value - startY
  
  if (dy >= 0) {
    aimLine.value = null
    return
  }
  
  const length = 100
  const magnitude = Math.sqrt(dx * dx + dy * dy)
  const normalizedDx = (dx / magnitude) * length
  const normalizedDy = (dy / magnitude) * length
  
  aimLine.value = {
    startX,
    startY,
    endX: startX + normalizedDx,
    endY: startY + normalizedDy
  }
}

// Handle click to shoot
const handleClick = (event: MouseEvent) => {
  if (gameResult.value || isPaused.value || shootingBubble.value) return
  
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const targetX = event.clientX - rect.left
  const targetY = event.clientY - rect.top
  
  shootBubble(targetX, targetY)
}

// Shoot bubble
const shootBubble = (targetX: number, targetY: number) => {
  if (!currentBubble.value) return
  
  const startX = shooterX.value + BUBBLE_SIZE / 2
  const startY = boardHeight.value - 40
  
  const dx = targetX - startX
  const dy = targetY - startY
  
  if (dy >= 0) return // Don't shoot downward
  
  const magnitude = Math.sqrt(dx * dx + dy * dy)
  const vx = (dx / magnitude) * SHOOTING_SPEED
  const vy = (dy / magnitude) * SHOOTING_SPEED
  
  shootingBubble.value = {
    x: startX - BUBBLE_SIZE / 2,
    y: startY - BUBBLE_SIZE / 2,
    color: currentBubble.value.color,
    vx,
    vy
  }
  
  // Move to next bubble
  currentBubble.value = nextBubble.value
  nextBubble.value = { color: getRandomColor() }
  
  aimLine.value = null
  
  // Start animation
  animateShooting()
}

// Animate shooting bubble
const animateShooting = () => {
  if (!shootingBubble.value) return
  
  const bubble = shootingBubble.value
  
  // Update position
  bubble.x += bubble.vx
  bubble.y += bubble.vy
  
  // Check wall collisions
  if (bubble.x <= 0 || bubble.x >= boardWidth.value - BUBBLE_SIZE) {
    bubble.vx = -bubble.vx
    bubble.x = Math.max(0, Math.min(boardWidth.value - BUBBLE_SIZE, bubble.x))
  }
  
  // Check if reached top
  if (bubble.y <= 0) {
    attachBubble(bubble)
    return
  }
  
  // Check collision with existing bubbles
  const collision = checkBubbleCollision(bubble)
  if (collision) {
    attachBubble(bubble)
    return
  }
  
  // Continue animation
  animationId.value = requestAnimationFrame(animateShooting)
}

// Check collision with existing bubbles
const checkBubbleCollision = (shootingBubble: ShootingBubble): boolean => {
  const bubbleCenter = {
    x: shootingBubble.x + BUBBLE_SIZE / 2,
    y: shootingBubble.y + BUBBLE_SIZE / 2
  }
  
  for (const bubble of bubbles.value) {
    const distance = Math.sqrt(
      Math.pow(bubbleCenter.x - bubble.x, 2) + 
      Math.pow(bubbleCenter.y - bubble.y, 2)
    )
    
    if (distance < BUBBLE_SIZE * 0.9) {
      return true
    }
  }
  
  return false
}

// Attach bubble to grid
const attachBubble = (bubble: ShootingBubble) => {
  // Find the best position to attach
  const attachPos = findAttachPosition(bubble)
  
  // Add bubble to grid
  bubbles.value.push({
    x: attachPos.x,
    y: attachPos.y,
    color: bubble.color,
    row: attachPos.row,
    col: attachPos.col
  })
  
  shootingBubble.value = null
  
  // Check for matches
  setTimeout(() => {
    processMatches(attachPos)
  }, 100)
}

// Find position to attach bubble
const findAttachPosition = (bubble: ShootingBubble) => {
  const bubbleCenter = {
    x: bubble.x + BUBBLE_SIZE / 2,
    y: bubble.y + BUBBLE_SIZE / 2
  }
  
  // Find the closest valid grid position
  let bestPos = { x: bubbleCenter.x, y: 0, row: 0, col: 0 }
  let minDistance = Infinity
  
  for (let row = 0; row < ROWS; row++) {
    const colsInRow = row % 2 === 0 ? COLS : COLS - 1
    const offsetX = row % 2 === 0 ? 0 : BUBBLE_SIZE / 2
    
    for (let col = 0; col < colsInRow; col++) {
      const gridX = col * BUBBLE_SIZE + offsetX + BUBBLE_SIZE / 2
      const gridY = row * (BUBBLE_SIZE * 0.87) + BUBBLE_SIZE / 2
      
      // Check if position is empty
      const occupied = bubbles.value.some(b => 
        Math.abs(b.x - gridX) < 5 && Math.abs(b.y - gridY) < 5
      )
      
      if (!occupied) {
        const distance = Math.sqrt(
          Math.pow(bubbleCenter.x - gridX, 2) + 
          Math.pow(bubbleCenter.y - gridY, 2)
        )
        
        if (distance < minDistance) {
          minDistance = distance
          bestPos = { x: gridX, y: gridY, row, col }
        }
      }
    }
  }
  
  return bestPos
}

// Process matches
const processMatches = (attachPos: { x: number, y: number, row: number, col: number }) => {
  const matchedBubbles = findMatches(attachPos)
  
  if (matchedBubbles.length >= 3) {
    // Remove matched bubbles
    for (const matched of matchedBubbles) {
      const index = bubbles.value.findIndex(b => 
        Math.abs(b.x - matched.x) < 5 && Math.abs(b.y - matched.y) < 5
      )
      if (index !== -1) {
        bubbles.value.splice(index, 1)
      }
    }
    
    // Update score
    score.value += matchedBubbles.length * 10 * level.value
    
    // Remove floating bubbles
    removeFloatingBubbles()
    
    // Check win condition
    if (bubbles.value.length === 0) {
      gameResult.value = {
        title: 'Level Complete!',
        message: `Score: ${score.value}`
      }
      setTimeout(() => {
        level.value++
        initializeGame()
      }, 2000)
    }
  }
  
  // Check lose condition
  const maxY = Math.max(...bubbles.value.map(b => b.y))
  if (maxY > boardHeight.value - 100) {
    gameResult.value = {
      title: 'Game Over!',
      message: `Final Score: ${score.value}`
    }
  }
}

// Find matching bubbles
const findMatches = (startPos: { x: number, y: number }): Bubble[] => {
  const startBubble = bubbles.value.find(b => 
    Math.abs(b.x - startPos.x) < 5 && Math.abs(b.y - startPos.y) < 5
  )
  
  if (!startBubble) return []
  
  const matches: Bubble[] = []
  const visited = new Set<string>()
  const queue = [startBubble]
  
  while (queue.length > 0) {
    const current = queue.shift()!
    const key = `${current.x},${current.y}`
    
    if (visited.has(key)) continue
    visited.add(key)
    matches.push(current)
    
    // Find adjacent bubbles of same color
    const adjacent = getAdjacentBubbles(current)
    for (const adj of adjacent) {
      const adjKey = `${adj.x},${adj.y}`
      if (!visited.has(adjKey) && adj.color === startBubble.color) {
        queue.push(adj)
      }
    }
  }
  
  return matches
}

// Get adjacent bubbles
const getAdjacentBubbles = (bubble: Bubble): Bubble[] => {
  const adjacent: Bubble[] = []
  const threshold = BUBBLE_SIZE * 1.1
  
  for (const other of bubbles.value) {
    if (other === bubble) continue
    
    const distance = Math.sqrt(
      Math.pow(bubble.x - other.x, 2) + 
      Math.pow(bubble.y - other.y, 2)
    )
    
    if (distance < threshold) {
      adjacent.push(other)
    }
  }
  
  return adjacent
}

// Remove floating bubbles
const removeFloatingBubbles = () => {
  const connected = new Set<string>()
  
  // Find all bubbles connected to top row
  const topBubbles = bubbles.value.filter(b => b.row === 0)
  const queue = [...topBubbles]
  
  while (queue.length > 0) {
    const current = queue.shift()!
    const key = `${current.x},${current.y}`
    
    if (connected.has(key)) continue
    connected.add(key)
    
    const adjacent = getAdjacentBubbles(current)
    for (const adj of adjacent) {
      const adjKey = `${adj.x},${adj.y}`
      if (!connected.has(adjKey)) {
        queue.push(adj)
      }
    }
  }
  
  // Remove unconnected bubbles
  const connectedBubbles = bubbles.value.filter(b => 
    connected.has(`${b.x},${b.y}`)
  )
  
  const removedCount = bubbles.value.length - connectedBubbles.length
  bubbles.value = connectedBubbles
  
  // Bonus score for floating bubbles
  if (removedCount > 0) {
    score.value += removedCount * 20 * level.value
  }
}

// Pause/resume game
const pauseGame = () => {
  isPaused.value = !isPaused.value
}

// Reset game
const resetGame = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
  initializeGame()
}

// Responsive board size
const updateBoardSize = () => {
  const maxWidth = Math.min(window.innerWidth - 40, 500)
  boardWidth.value = Math.max(300, maxWidth)
  boardHeight.value = boardWidth.value * 1.33
  updateShooterPosition()
}

onMounted(() => {
  initializeGame()
  updateBoardSize()
  window.addEventListener('resize', updateBoardSize)
})

onUnmounted(() => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
  window.removeEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.bubble-board {
  position: relative;
  background: linear-gradient(to bottom, #e0f2fe, #b3e5fc);
  border: 2px solid #0277bd;
  border-radius: 12px;
  overflow: hidden;
  cursor: crosshair;
}

.board-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 60%, rgba(255,255,255,0.2) 0%, transparent 50%);
}

.bubble {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid rgba(255,255,255,0.3);
  box-shadow: 
    inset 0 2px 4px rgba(255,255,255,0.4),
    0 2px 8px rgba(0,0,0,0.2);
  transition: all 0.1s ease;
}

.bubble.small {
  width: 20px;
  height: 20px;
}

.bubble.shooting {
  z-index: 10;
}

.bubble-inner {
  position: absolute;
  top: 15%;
  left: 15%;
  width: 30%;
  height: 30%;
  background: rgba(255,255,255,0.6);
  border-radius: 50%;
  filter: blur(1px);
}

.aim-line {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 5;
}

.shooter {
  position: absolute;
  z-index: 15;
}

.shooter-base {
  width: 40px;
  height: 20px;
  background: linear-gradient(to bottom, #4a5568, #2d3748);
  border-radius: 20px 20px 0 0;
  margin: 0 auto;
}

.current-bubble {
  position: relative;
  margin: -15px auto 0;
}

.next-bubble-preview {
  position: absolute;
  text-align: center;
  z-index: 15;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
