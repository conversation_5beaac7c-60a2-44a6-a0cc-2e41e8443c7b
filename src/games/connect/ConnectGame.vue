<template>
  <div class="connect-game">
    <!-- Game Status -->
    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('games.connect.name') }}</h2>
      <div class="flex justify-center items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ score }}</div>
          <div class="text-sm text-gray-500">Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ level }}</div>
          <div class="text-sm text-gray-500">Level</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ timeLeft }}</div>
          <div class="text-sm text-gray-500">Time</div>
        </div>
      </div>
      <p class="text-gray-600 mt-2">{{ gameStatus }}</p>
    </div>

    <!-- Game Board -->
    <div class="connect-board mx-auto" :style="{ width: boardSize + 'px', height: boardSize + 'px' }">
      <!-- Grid lines -->
      <svg class="grid-lines" :width="boardSize" :height="boardSize">
        <!-- Vertical lines -->
        <line
          v-for="i in gridSize + 1"
          :key="'v' + i"
          :x1="(i - 1) * cellSize"
          :y1="0"
          :x2="(i - 1) * cellSize"
          :y2="boardSize"
          stroke="#e5e7eb"
          stroke-width="1"
        />
        <!-- Horizontal lines -->
        <line
          v-for="i in gridSize + 1"
          :key="'h' + i"
          :x1="0"
          :y1="(i - 1) * cellSize"
          :x2="boardSize"
          :y2="(i - 1) * cellSize"
          stroke="#e5e7eb"
          stroke-width="1"
        />
      </svg>

      <!-- Items -->
      <div
        v-for="(item, index) in items"
        :key="index"
        class="connect-item"
        :class="{
          'selected': selectedItems.includes(index),
          'matched': item.matched,
          'highlighted': highlightedPath.includes(index)
        }"
        :style="{
          left: item.x * cellSize + cellSize / 2 - 20 + 'px',
          top: item.y * cellSize + cellSize / 2 - 20 + 'px',
          backgroundColor: getItemColor(item.type)
        }"
        @click="selectItem(index)"
      >
        <div class="item-inner">{{ getItemSymbol(item.type) }}</div>
      </div>

      <!-- Connection lines -->
      <svg class="connection-lines" :width="boardSize" :height="boardSize">
        <path
          v-for="(path, index) in connectionPaths"
          :key="index"
          :d="path.d"
          stroke="#3b82f6"
          stroke-width="3"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="connection-path"
        />
      </svg>

      <!-- Preview path -->
      <svg v-if="previewPath" class="preview-path" :width="boardSize" :height="boardSize">
        <path
          :d="previewPath"
          stroke="#10b981"
          stroke-width="2"
          fill="none"
          stroke-dasharray="5,5"
          stroke-linecap="round"
          opacity="0.7"
        />
      </svg>
    </div>

    <!-- Game Controls -->
    <div class="mt-6 flex justify-center space-x-4">
      <button @click="resetGame" class="btn btn-secondary">
        New Game
      </button>
      <button @click="shuffleItems" class="btn btn-primary" :disabled="!canShuffle">
        Shuffle
      </button>
      <button @click="getHint" class="btn btn-primary" :disabled="!canGetHint">
        Hint
      </button>
    </div>

    <!-- Game Result -->
    <div v-if="gameResult" class="mt-6 text-center">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto shadow-lg">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ gameResult.title }}</h3>
        <p class="text-gray-600 mb-4">{{ gameResult.message }}</p>
        <button @click="resetGame" class="btn btn-primary">
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// Types
interface Item {
  x: number
  y: number
  type: number
  matched: boolean
}

interface Point {
  x: number
  y: number
}

interface ConnectionPath {
  d: string
  points: Point[]
}

interface GameResult {
  title: string
  message: string
}

// Constants
const GRID_SIZE = 8
const ITEM_TYPES = 6
const COLORS = ['#ef4444', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#f97316']
const SYMBOLS = ['🔴', '🔵', '🟢', '🟡', '🟣', '🟠']
const TIME_LIMIT = 300 // 5 minutes

// Reactive state
const items = ref<Item[]>([])
const selectedItems = ref<number[]>([])
const highlightedPath = ref<number[]>([])
const connectionPaths = ref<ConnectionPath[]>([])
const previewPath = ref<string>('')
const score = ref(0)
const level = ref(1)
const timeLeft = ref(TIME_LIMIT)
const gameResult = ref<GameResult | null>(null)
const boardSize = ref(400)
const gridSize = ref(GRID_SIZE)
const cellSize = ref(50)
const canShuffle = ref(true)
const canGetHint = ref(true)
const gameTimer = ref<number | null>(null)

// Computed
const gameStatus = computed(() => {
  if (gameResult.value) return gameResult.value.message
  if (timeLeft.value <= 0) return 'Time\'s up!'
  return 'Connect matching items with clear paths!'
})

// Initialize game
const initializeGame = () => {
  items.value = []
  selectedItems.value = []
  highlightedPath.value = []
  connectionPaths.value = []
  previewPath.value = ''
  score.value = 0
  level.value = 1
  timeLeft.value = TIME_LIMIT
  gameResult.value = null
  canShuffle.value = true
  canGetHint.value = true
  
  generateItems()
  startTimer()
  updateBoardSize()
}

// Generate items
const generateItems = () => {
  const totalItems = gridSize.value * gridSize.value
  const itemsPerType = Math.floor(totalItems / ITEM_TYPES)
  const itemTypes: number[] = []
  
  // Create pairs of items
  for (let type = 0; type < ITEM_TYPES; type++) {
    for (let i = 0; i < itemsPerType; i++) {
      itemTypes.push(type)
    }
  }
  
  // Fill remaining slots
  while (itemTypes.length < totalItems) {
    itemTypes.push(Math.floor(Math.random() * ITEM_TYPES))
  }
  
  // Shuffle item types
  for (let i = itemTypes.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[itemTypes[i], itemTypes[j]] = [itemTypes[j], itemTypes[i]]
  }
  
  // Place items on grid
  let index = 0
  for (let y = 0; y < gridSize.value; y++) {
    for (let x = 0; x < gridSize.value; x++) {
      items.value.push({
        x,
        y,
        type: itemTypes[index],
        matched: false
      })
      index++
    }
  }
}

// Select item
const selectItem = (index: number) => {
  if (gameResult.value || items.value[index].matched) return
  
  if (selectedItems.value.length === 0) {
    // First selection
    selectedItems.value = [index]
    highlightedPath.value = []
    previewPath.value = ''
  } else if (selectedItems.value.length === 1) {
    const firstIndex = selectedItems.value[0]
    
    if (index === firstIndex) {
      // Deselect
      selectedItems.value = []
      highlightedPath.value = []
      previewPath.value = ''
    } else if (items.value[index].type === items.value[firstIndex].type) {
      // Try to connect matching items
      const path = findPath(firstIndex, index)
      if (path.length > 0) {
        // Valid connection
        connectItems(firstIndex, index, path)
      } else {
        // No valid path, select new item
        selectedItems.value = [index]
        highlightedPath.value = []
        previewPath.value = ''
      }
    } else {
      // Different type, select new item
      selectedItems.value = [index]
      highlightedPath.value = []
      previewPath.value = ''
    }
  }
  
  // Update preview path
  if (selectedItems.value.length === 1) {
    updatePreviewPath(selectedItems.value[0], index)
  }
}

// Find path between two items
const findPath = (startIndex: number, endIndex: number): Point[] => {
  const start = items.value[startIndex]
  const end = items.value[endIndex]
  
  // Try different path types: straight lines and one turn
  const paths = [
    // Direct horizontal then vertical
    getPathWithOneTurn(start, end, true),
    // Direct vertical then horizontal
    getPathWithOneTurn(start, end, false),
    // Direct line (horizontal or vertical)
    getDirectPath(start, end)
  ]
  
  for (const path of paths) {
    if (path.length > 0 && isPathClear(path, startIndex, endIndex)) {
      return path
    }
  }
  
  return []
}

// Get direct path (horizontal or vertical only)
const getDirectPath = (start: Item, end: Item): Point[] => {
  if (start.x === end.x) {
    // Vertical line
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)
    const path: Point[] = []
    for (let y = minY; y <= maxY; y++) {
      path.push({ x: start.x, y })
    }
    return path
  } else if (start.y === end.y) {
    // Horizontal line
    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    const path: Point[] = []
    for (let x = minX; x <= maxX; x++) {
      path.push({ x, y: start.y })
    }
    return path
  }
  
  return []
}

// Get path with one turn
const getPathWithOneTurn = (start: Item, end: Item, horizontalFirst: boolean): Point[] => {
  const path: Point[] = []
  
  if (horizontalFirst) {
    // Go horizontal first, then vertical
    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    
    // Horizontal segment
    for (let x = minX; x <= maxX; x++) {
      path.push({ x, y: start.y })
    }
    
    // Vertical segment
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)
    for (let y = minY + 1; y <= maxY; y++) {
      path.push({ x: end.x, y })
    }
  } else {
    // Go vertical first, then horizontal
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)
    
    // Vertical segment
    for (let y = minY; y <= maxY; y++) {
      path.push({ x: start.x, y })
    }
    
    // Horizontal segment
    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    for (let x = minX + 1; x <= maxX; x++) {
      path.push({ x, y: end.y })
    }
  }
  
  return path
}

// Check if path is clear
const isPathClear = (path: Point[], startIndex: number, endIndex: number): boolean => {
  for (const point of path) {
    const itemIndex = getItemIndexAt(point.x, point.y)
    if (itemIndex !== -1 && itemIndex !== startIndex && itemIndex !== endIndex && !items.value[itemIndex].matched) {
      return false
    }
  }
  return true
}

// Get item index at position
const getItemIndexAt = (x: number, y: number): number => {
  return items.value.findIndex(item => item.x === x && item.y === y && !item.matched)
}

// Connect items
const connectItems = (startIndex: number, endIndex: number, path: Point[]) => {
  // Mark items as matched
  items.value[startIndex].matched = true
  items.value[endIndex].matched = true
  
  // Create connection path for animation
  const pathString = createPathString(path)
  connectionPaths.value.push({
    d: pathString,
    points: path
  })
  
  // Update score
  score.value += 10 * level.value
  
  // Clear selection
  selectedItems.value = []
  highlightedPath.value = []
  previewPath.value = ''
  
  // Remove connection path after animation
  setTimeout(() => {
    connectionPaths.value.shift()
  }, 1000)
  
  // Check win condition
  if (items.value.every(item => item.matched)) {
    levelComplete()
  }
}

// Create SVG path string
const createPathString = (points: Point[]): string => {
  if (points.length === 0) return ''
  
  const start = points[0]
  let pathString = `M ${start.x * cellSize.value + cellSize.value / 2} ${start.y * cellSize.value + cellSize.value / 2}`
  
  for (let i = 1; i < points.length; i++) {
    const point = points[i]
    pathString += ` L ${point.x * cellSize.value + cellSize.value / 2} ${point.y * cellSize.value + cellSize.value / 2}`
  }
  
  return pathString
}

// Update preview path
const updatePreviewPath = (startIndex: number, hoverIndex: number) => {
  if (items.value[startIndex].type === items.value[hoverIndex].type) {
    const path = findPath(startIndex, hoverIndex)
    if (path.length > 0) {
      previewPath.value = createPathString(path)
      return
    }
  }
  previewPath.value = ''
}

// Level complete
const levelComplete = () => {
  level.value++
  timeLeft.value += 60 // Bonus time
  
  // Increase difficulty
  if (level.value % 3 === 0 && gridSize.value < 10) {
    gridSize.value++
    updateBoardSize()
  }
  
  // Generate new level
  setTimeout(() => {
    generateItems()
  }, 1000)
}

// Shuffle items
const shuffleItems = () => {
  if (!canShuffle.value) return
  
  const unmatchedItems = items.value.filter(item => !item.matched)
  const types = unmatchedItems.map(item => item.type)
  
  // Shuffle types
  for (let i = types.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[types[i], types[j]] = [types[j], types[i]]
  }
  
  // Reassign types
  let typeIndex = 0
  for (const item of items.value) {
    if (!item.matched) {
      item.type = types[typeIndex++]
    }
  }
  
  selectedItems.value = []
  highlightedPath.value = []
  previewPath.value = ''
  canShuffle.value = false
  
  // Re-enable shuffle after delay
  setTimeout(() => {
    canShuffle.value = true
  }, 10000)
}

// Get hint
const getHint = () => {
  if (!canGetHint.value) return
  
  // Find a valid pair
  for (let i = 0; i < items.value.length; i++) {
    if (items.value[i].matched) continue
    
    for (let j = i + 1; j < items.value.length; j++) {
      if (items.value[j].matched) continue
      
      if (items.value[i].type === items.value[j].type) {
        const path = findPath(i, j)
        if (path.length > 0) {
          // Highlight the pair
          highlightedPath.value = [i, j]
          
          setTimeout(() => {
            highlightedPath.value = []
          }, 3000)
          
          canGetHint.value = false
          setTimeout(() => {
            canGetHint.value = true
          }, 15000)
          
          return
        }
      }
    }
  }
}

// Get item color
const getItemColor = (type: number): string => {
  return COLORS[type] || '#gray'
}

// Get item symbol
const getItemSymbol = (type: number): string => {
  return SYMBOLS[type] || '⚫'
}

// Start timer
const startTimer = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
  }
  
  gameTimer.value = setInterval(() => {
    timeLeft.value--
    
    if (timeLeft.value <= 0) {
      gameOver()
    }
  }, 1000)
}

// Game over
const gameOver = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
    gameTimer.value = null
  }
  
  gameResult.value = {
    title: 'Game Over!',
    message: `Final Score: ${score.value} | Level: ${level.value}`
  }
}

// Reset game
const resetGame = () => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
    gameTimer.value = null
  }
  
  gridSize.value = GRID_SIZE
  initializeGame()
}

// Update board size
const updateBoardSize = () => {
  const maxSize = Math.min(window.innerWidth - 40, 500)
  boardSize.value = Math.max(300, maxSize)
  cellSize.value = boardSize.value / gridSize.value
}

onMounted(() => {
  initializeGame()
  window.addEventListener('resize', updateBoardSize)
})

onUnmounted(() => {
  if (gameTimer.value) {
    clearInterval(gameTimer.value)
  }
  window.removeEventListener('resize', updateBoardSize)
})
</script>

<style scoped>
.connect-board {
  position: relative;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #cbd5e1;
  border-radius: 12px;
  overflow: hidden;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.connect-item {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255,255,255,0.3);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.connect-item:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.25);
}

.connect-item.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transform: scale(1.15);
}

.connect-item.matched {
  opacity: 0.3;
  transform: scale(0.8);
  pointer-events: none;
}

.connect-item.highlighted {
  animation: pulse 1s infinite;
}

.item-inner {
  font-size: 1.2em;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
}

.connection-lines,
.preview-path {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 5;
}

.connection-path {
  animation: drawPath 0.5s ease-out;
}

.preview-path path {
  animation: dashMove 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes drawPath {
  from {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
  }
}

@keyframes dashMove {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 20;
  }
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
