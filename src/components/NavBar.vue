<template>
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo and Brand -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">P8</span>
            </div>
            <span class="text-xl font-bold text-gray-900">Poy8 Eliminating Game</span>
          </router-link>
        </div>

        <!-- Navigation Links -->
        <div class="hidden md:flex items-center space-x-8">
          <a 
            href="https://poy8.com" 
            target="_blank" 
            rel="noopener noreferrer"
            class="text-gray-600 hover:text-blue-600 font-medium transition-colors"
          >
            {{ $t('nav.poy8') }}
          </a>
          <router-link 
            to="/" 
            class="text-gray-600 hover:text-blue-600 font-medium transition-colors"
            :class="{ 'text-blue-600': $route.name === 'home' }"
          >
            {{ $t('nav.home') }}
          </router-link>
          <router-link 
            to="/about" 
            class="text-gray-600 hover:text-blue-600 font-medium transition-colors"
            :class="{ 'text-blue-600': $route.name === 'about' }"
          >
            {{ $t('nav.about') }}
          </router-link>
        </div>

        <!-- Language Selector and Mobile Menu -->
        <div class="flex items-center space-x-4">
          <LanguageSelector />
          
          <!-- Mobile menu button -->
          <button 
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="md:hidden p-2 rounded-md text-gray-600 hover:text-blue-600 hover:bg-gray-100"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div v-if="mobileMenuOpen" class="md:hidden py-4 border-t border-gray-200">
        <div class="flex flex-col space-y-4">
          <a 
            href="https://poy8.com" 
            target="_blank" 
            rel="noopener noreferrer"
            class="text-gray-600 hover:text-blue-600 font-medium"
          >
            {{ $t('nav.poy8') }}
          </a>
          <router-link 
            to="/" 
            class="text-gray-600 hover:text-blue-600 font-medium"
            @click="mobileMenuOpen = false"
          >
            {{ $t('nav.home') }}
          </router-link>
          <router-link 
            to="/about" 
            class="text-gray-600 hover:text-blue-600 font-medium"
            @click="mobileMenuOpen = false"
          >
            {{ $t('nav.about') }}
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LanguageSelector from './LanguageSelector.vue'

const mobileMenuOpen = ref(false)
</script>
