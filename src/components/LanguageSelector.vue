<template>
  <div class="relative">
    <button
      @click="dropdownOpen = !dropdownOpen"
      class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-md transition-colors"
    >
      <span>{{ currentLocale.flag }}</span>
      <span class="hidden sm:block">{{ currentLocale.name }}</span>
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <!-- Dropdown -->
    <div
      v-if="dropdownOpen"
      class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
    >
      <div class="py-1">
        <button
          v-for="locale in supportedLocales"
          :key="locale.code"
          @click="changeLanguage(locale.code)"
          class="flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-blue-50 text-blue-600': locale.code === currentLocale.code }"
        >
          <span>{{ locale.flag }}</span>
          <span>{{ locale.name }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { supportedLocales } from '../locales'

const { locale } = useI18n()
const dropdownOpen = ref(false)

const currentLocale = computed(() => {
  return supportedLocales.find(l => l.code === locale.value) || supportedLocales[0]
})

const changeLanguage = (newLocale: string) => {
  locale.value = newLocale
  localStorage.setItem('preferred-language', newLocale)
  dropdownOpen.value = false
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    dropdownOpen.value = false
  }
}

onMounted(() => {
  // Load saved language preference
  const savedLanguage = localStorage.getItem('preferred-language')
  if (savedLanguage && supportedLocales.some(l => l.code === savedLanguage)) {
    locale.value = savedLanguage
  }
  
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
