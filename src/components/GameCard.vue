<template>
  <div class="game-card group">
    <!-- Game Image -->
    <div class="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl overflow-hidden">
      <div class="absolute inset-0 flex items-center justify-center">
        <!-- Placeholder for game image -->
        <div class="w-24 h-24 bg-white rounded-lg shadow-lg flex items-center justify-center">
          <component :is="gameIcon" class="w-12 h-12 text-gray-600" />
        </div>
      </div>
      
      <!-- Overlay on hover -->
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
        <button
          @click="$emit('play', game.id)"
          class="btn btn-primary opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300"
        >
          {{ $t('home.playNow') }}
        </button>
      </div>
    </div>

    <!-- Game Info -->
    <div class="p-6">
      <h3 class="text-xl font-bold text-gray-900 mb-2">
        {{ game.name }}
      </h3>
      
      <p class="text-gray-600 text-sm mb-4 line-clamp-2">
        {{ game.description }}
      </p>

      <!-- Game Details -->
      <div class="flex justify-between items-center text-xs text-gray-500 mb-4">
        <span class="bg-gray-100 px-2 py-1 rounded">{{ game.difficulty }}</span>
        <span class="bg-gray-100 px-2 py-1 rounded">{{ game.players }}</span>
      </div>

      <!-- Action Button -->
      <button
        @click="$emit('play', game.id)"
        class="w-full btn btn-primary"
      >
        {{ $t('home.playNow') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Game {
  id: string
  name: string
  description: string
  image: string
  difficulty: string
  players: string
}

const props = defineProps<{
  game: Game
}>()

defineEmits<{
  play: [gameId: string]
}>()

// Game icons based on game type
const gameIcon = computed(() => {
  switch (props.game.id) {
    case 'chess':
      return 'ChessIcon'
    case 'checkers':
      return 'CheckersIcon'
    case 'reversi':
      return 'ReversiIcon'
    case 'gomoku':
      return 'GomokuIcon'
    default:
      return 'DefaultGameIcon'
  }
})

// Simple SVG icons as components
const ChessIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 22H5v-2h14v2M17.5 2.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5M12 6.5c-1.38 0-2.5 1.12-2.5 2.5s1.12 2.5 2.5 2.5 2.5-1.12 2.5-2.5-1.12-2.5-2.5-2.5M6.5 2.5C5.67 2.5 5 3.17 5 4s.67 1.5 1.5 1.5S8 4.83 8 4s-.67-1.5-1.5-1.5M12 12l-2 8h4l-2-8z"/>
    </svg>
  `
}

const CheckersIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>
  `
}

const ReversiIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
      <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6"/>
    </svg>
  `
}

const GomokuIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    </svg>
  `
}

const DefaultGameIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>
  `
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
