<template>
  <div class="min-h-screen bg-gray-50">
    <div v-if="!gameData" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Game Not Found</h1>
        <router-link to="/" class="btn btn-primary">{{ $t('common.back') }}</router-link>
      </div>
    </div>

    <div v-else>
      <!-- Game Header -->
      <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="flex items-center space-x-4 mb-6">
            <router-link 
              to="/" 
              class="text-gray-600 hover:text-blue-600 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </router-link>
            <h1 class="text-3xl font-bold text-gray-900">{{ gameData.name }}</h1>
          </div>
          
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
              <p class="text-lg text-gray-600 mb-2">{{ gameData.description }}</p>
              <div class="flex items-center space-x-4 text-sm text-gray-500">
                <span class="bg-gray-100 px-3 py-1 rounded-full">{{ gameData.difficulty }}</span>
                <span class="bg-gray-100 px-3 py-1 rounded-full">{{ gameData.players }}</span>
              </div>
            </div>
            <button 
              @click="startGame"
              class="btn btn-primary text-lg px-8 py-3"
            >
              {{ $t('home.playNow') }}
            </button>
          </div>
        </div>
      </section>

      <!-- Game Container -->
      <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- Game Board Area -->
          <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <div v-if="!gameStarted" class="aspect-square max-w-2xl mx-auto bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <div class="w-24 h-24 bg-white rounded-lg shadow-lg flex items-center justify-center mx-auto mb-4">
                  <component :is="gameIcon" class="w-12 h-12 text-gray-600" />
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ gameData.name }}</h3>
                <p class="text-gray-600 mb-4">Click to start playing!</p>
                <button
                  @click="startGame"
                  class="btn btn-primary"
                >
                  {{ $t('home.playNow') }}
                </button>
              </div>
            </div>

            <!-- Actual Game Component -->
            <div v-else>
              <component :is="gameComponent" />
            </div>
          </div>

          <!-- Content Sections -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
              <!-- Game Tags -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.tags') }}</h2>
                <div class="flex flex-wrap gap-2">
                  <span v-for="tag in gameTags" :key="tag" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                    {{ tag }}
                  </span>
                </div>
              </div>

              <!-- Game Introduction -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.gameIntro') }}</h2>
                <div class="prose prose-gray max-w-none">
                  <h3>{{ $t('gameDetail.history') }}</h3>
                  <p>{{ gameData.history }}</p>
                  
                  <h3>{{ $t('gameDetail.howToPlay') }}</h3>
                  <p>{{ gameData.description }}</p>
                  
                  <h3>{{ $t('gameDetail.tips') }}</h3>
                  <ul>
                    <li v-for="tip in gameTips" :key="tip">{{ tip }}</li>
                  </ul>
                </div>
              </div>

              <!-- Video Recommendations -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.videoRecommendations') }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div v-for="video in videoRecommendations" :key="video.id" class="bg-gray-100 rounded-lg p-4">
                    <div class="aspect-video bg-gray-200 rounded-lg mb-3 flex items-center justify-center">
                      <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ video.title }}</h4>
                    <p class="text-sm text-gray-600">{{ video.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
              <!-- Other Games -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.otherGames') }}</h2>
                <div class="space-y-4">
                  <div 
                    v-for="otherGame in otherGames" 
                    :key="otherGame.id"
                    class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                    @click="$router.push(`/game/${otherGame.id}`)"
                  >
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <component :is="getGameIcon(otherGame.id)" class="w-6 h-6 text-gray-600" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">{{ otherGame.name }}</h4>
                      <p class="text-sm text-gray-600">{{ otherGame.players }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Match3Game from '../games/match3/Match3Game.vue'
import BubbleGame from '../games/bubble/BubbleGame.vue'
import TetrisGame from '../games/tetris/TetrisGame.vue'
import ConnectGame from '../games/connect/ConnectGame.vue'
import JewelsGame from '../games/jewels/JewelsGame.vue'
import ZumaGame from '../games/zuma/ZumaGame.vue'
import MahjongGame from '../games/mahjong/MahjongGame.vue'
import CollapseGame from '../games/collapse/CollapseGame.vue'

const route = useRoute()
const { t } = useI18n()

const props = defineProps<{
  gameId: string
}>()

const gameStarted = ref(false)

// Game data
const gameData = computed(() => {
  const games = {
    match3: {
      id: 'match3',
      name: t('games.match3.name'),
      description: t('games.match3.description'),
      history: t('games.match3.history'),
      difficulty: t('games.match3.difficulty'),
      players: t('games.match3.players')
    },
    bubble: {
      id: 'bubble',
      name: t('games.bubble.name'),
      description: t('games.bubble.description'),
      history: t('games.bubble.history'),
      difficulty: t('games.bubble.difficulty'),
      players: t('games.bubble.players')
    },
    tetris: {
      id: 'tetris',
      name: t('games.tetris.name'),
      description: t('games.tetris.description'),
      history: t('games.tetris.history'),
      difficulty: t('games.tetris.difficulty'),
      players: t('games.tetris.players')
    },
    connect: {
      id: 'connect',
      name: t('games.connect.name'),
      description: t('games.connect.description'),
      history: t('games.connect.history'),
      difficulty: t('games.connect.difficulty'),
      players: t('games.connect.players')
    },
    jewels: {
      id: 'jewels',
      name: t('games.jewels.name'),
      description: t('games.jewels.description'),
      history: t('games.jewels.history'),
      difficulty: t('games.jewels.difficulty'),
      players: t('games.jewels.players')
    },
    zuma: {
      id: 'zuma',
      name: t('games.zuma.name'),
      description: t('games.zuma.description'),
      history: t('games.zuma.history'),
      difficulty: t('games.zuma.difficulty'),
      players: t('games.zuma.players')
    },
    mahjong: {
      id: 'mahjong',
      name: t('games.mahjong.name'),
      description: t('games.mahjong.description'),
      history: t('games.mahjong.history'),
      difficulty: t('games.mahjong.difficulty'),
      players: t('games.mahjong.players')
    },
    collapse: {
      id: 'collapse',
      name: t('games.collapse.name'),
      description: t('games.collapse.description'),
      history: t('games.collapse.history'),
      difficulty: t('games.collapse.difficulty'),
      players: t('games.collapse.players')
    }
  }

  return games[props.gameId as keyof typeof games] || null
})

// Game tags
const gameTags = computed(() => {
  const tags = {
    match3: ['Puzzle', 'Casual', 'Match-3', 'Colorful'],
    bubble: ['Arcade', 'Shooting', 'Casual', 'Precision'],
    tetris: ['Puzzle', 'Classic', 'Falling Blocks', 'Strategy'],
    connect: ['Puzzle', 'Logic', 'Path Finding', 'Relaxing'],
    jewels: ['Match-3', 'Gems', 'Power-ups', 'Addictive'],
    zuma: ['Marble Shooter', 'Chain Reaction', 'Timing', 'Skill'],
    mahjong: ['Solitaire', 'Traditional', 'Memory', 'Patience'],
    collapse: ['Block Puzzle', 'Chain Reaction', 'Strategy', 'Satisfying']
  }

  return tags[props.gameId as keyof typeof tags] || []
})

// Game tips
const gameTips = computed(() => {
  const tips = {
    match3: [
      'Look for matches at the bottom first',
      'Create special gems by matching 4 or more',
      'Plan your moves to create cascades',
      'Save special gems for difficult situations'
    ],
    bubble: [
      'Aim for the ceiling to drop large groups',
      'Use walls to bounce shots',
      'Clear bubbles from the sides first',
      'Plan several shots ahead'
    ],
    tetris: [
      'Keep the stack low and flat',
      'Save the I-piece for Tetris clears',
      'Don\'t leave gaps in your stack',
      'Learn to rotate pieces quickly'
    ],
    connect: [
      'Look for the shortest paths first',
      'Clear edge pieces to open up paths',
      'Use the hint feature when stuck',
      'Plan ahead to avoid blocking yourself'
    ],
    jewels: [
      'Focus on creating special jewels',
      'Use power-ups strategically',
      'Match from the bottom to create cascades',
      'Save moves for difficult situations'
    ],
    zuma: [
      'Shoot at the front of the chain',
      'Create gaps to slow the chain',
      'Match colors that appear most often',
      'Use bank shots when necessary'
    ],
    mahjong: [
      'Remove tiles from the top layers first',
      'Focus on freeing blocked tiles',
      'Remember tile positions',
      'Use the shuffle feature wisely'
    ],
    collapse: [
      'Look for large groups of same colors',
      'Clear from the bottom to create cascades',
      'Plan moves to create bigger groups',
      'Use the shuffle when no good moves exist'
    ]
  }

  return tips[props.gameId as keyof typeof tips] || []
})

// Video recommendations (placeholder)
const videoRecommendations = computed(() => [
  {
    id: 1,
    title: `How to Play ${gameData.value?.name}`,
    description: 'Learn the basic rules and strategies'
  },
  {
    id: 2,
    title: `Advanced ${gameData.value?.name} Tactics`,
    description: 'Master advanced techniques and strategies'
  }
])

// Other games
const otherGames = computed(() => {
  const allGames = [
    { id: 'match3', name: t('games.match3.name'), players: t('games.match3.players') },
    { id: 'bubble', name: t('games.bubble.name'), players: t('games.bubble.players') },
    { id: 'tetris', name: t('games.tetris.name'), players: t('games.tetris.players') },
    { id: 'connect', name: t('games.connect.name'), players: t('games.connect.players') },
    { id: 'jewels', name: t('games.jewels.name'), players: t('games.jewels.players') },
    { id: 'zuma', name: t('games.zuma.name'), players: t('games.zuma.players') },
    { id: 'mahjong', name: t('games.mahjong.name'), players: t('games.mahjong.players') },
    { id: 'collapse', name: t('games.collapse.name'), players: t('games.collapse.players') }
  ]

  return allGames.filter(game => game.id !== props.gameId)
})

// Game icons
const gameIcon = computed(() => getGameIcon(props.gameId))

const getGameIcon = (gameId: string) => {
  const icons = {
    match3: 'Match3Icon',
    bubble: 'BubbleIcon',
    tetris: 'TetrisIcon',
    connect: 'ConnectIcon',
    jewels: 'JewelsIcon',
    zuma: 'ZumaIcon',
    mahjong: 'MahjongIcon',
    collapse: 'CollapseIcon'
  }

  return icons[gameId as keyof typeof icons] || 'DefaultGameIcon'
}

// Game component
const gameComponent = computed(() => {
  const components = {
    match3: Match3Game,
    bubble: BubbleGame,
    tetris: TetrisGame,
    connect: ConnectGame,
    jewels: JewelsGame,
    zuma: ZumaGame,
    mahjong: MahjongGame,
    collapse: CollapseGame
  }

  return components[props.gameId as keyof typeof components]
})

// Start game function
const startGame = () => {
  if (gameComponent.value) {
    gameStarted.value = true
  } else {
    alert(`${gameData.value?.name} game is coming soon!`)
  }
}

// Icon components for eliminating games
const Match3Icon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
    </svg>
  `
}

const BubbleIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
    </svg>
  `
}

const TetrisIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
    </svg>
  `
}

const ConnectIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    </svg>
  `
}

const JewelsIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M5 16L3 5h5.5l1.5 5 1.5-5H17l-2 11H5zm2.7-2h8.6l.9-5h-3.4l-1.8 6-1.8-6H6.8l.9 5z"/>
    </svg>
  `
}

const ZumaIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z"/>
    </svg>
  `
}

const MahjongIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
    </svg>
  `
}

const CollapseIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M6 2v6h.01L6 8.01 10 12l-4 4 .01.01H6V22h12v-5.99h-.01L18 16l-4-4 4-3.99-.01-.01H18V2H6zm10 14.5V20H8v-3.5l4-4 4 4zm0-9V4h-8v3.5L12 11.5 16 7.5z"/>
    </svg>
  `
}

const DefaultGameIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>
  `
}

onMounted(() => {
  // Update page title
  if (gameData.value) {
    document.title = `${gameData.value.name} - Poy8 Eliminating Game`
  }
})
</script>

<style scoped>
.prose h3 {
  @apply text-lg font-semibold text-gray-900 mt-6 mb-3;
}

.prose p {
  @apply text-gray-600 mb-4;
}

.prose ul {
  @apply list-disc list-inside text-gray-600 space-y-1;
}
</style>
