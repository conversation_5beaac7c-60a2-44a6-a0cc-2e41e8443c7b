<template>
  <div class="min-h-screen bg-gray-50">
    <div v-if="!gameData" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Game Not Found</h1>
        <router-link to="/" class="btn btn-primary">{{ $t('common.back') }}</router-link>
      </div>
    </div>

    <div v-else>
      <!-- Game Header -->
      <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="flex items-center space-x-4 mb-6">
            <router-link 
              to="/" 
              class="text-gray-600 hover:text-blue-600 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </router-link>
            <h1 class="text-3xl font-bold text-gray-900">{{ gameData.name }}</h1>
          </div>
          
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
              <p class="text-lg text-gray-600 mb-2">{{ gameData.description }}</p>
              <div class="flex items-center space-x-4 text-sm text-gray-500">
                <span class="bg-gray-100 px-3 py-1 rounded-full">{{ gameData.difficulty }}</span>
                <span class="bg-gray-100 px-3 py-1 rounded-full">{{ gameData.players }}</span>
              </div>
            </div>
            <button 
              @click="startGame"
              class="btn btn-primary text-lg px-8 py-3"
            >
              {{ $t('home.playNow') }}
            </button>
          </div>
        </div>
      </section>

      <!-- Game Container -->
      <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- Game Board Area -->
          <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <div v-if="!gameStarted" class="aspect-square max-w-2xl mx-auto bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <div class="w-24 h-24 bg-white rounded-lg shadow-lg flex items-center justify-center mx-auto mb-4">
                  <component :is="gameIcon" class="w-12 h-12 text-gray-600" />
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ gameData.name }}</h3>
                <p class="text-gray-600 mb-4">Click to start playing!</p>
                <button
                  @click="startGame"
                  class="btn btn-primary"
                >
                  {{ $t('home.playNow') }}
                </button>
              </div>
            </div>

            <!-- Actual Game Component -->
            <div v-else>
              <component :is="gameComponent" />
            </div>
          </div>

          <!-- Content Sections -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
              <!-- Game Tags -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.tags') }}</h2>
                <div class="flex flex-wrap gap-2">
                  <span v-for="tag in gameTags" :key="tag" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                    {{ tag }}
                  </span>
                </div>
              </div>

              <!-- Game Introduction -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.gameIntro') }}</h2>
                <div class="prose prose-gray max-w-none">
                  <h3>{{ $t('gameDetail.history') }}</h3>
                  <p>{{ gameData.history }}</p>
                  
                  <h3>{{ $t('gameDetail.howToPlay') }}</h3>
                  <p>{{ gameData.description }}</p>
                  
                  <h3>{{ $t('gameDetail.tips') }}</h3>
                  <ul>
                    <li v-for="tip in gameTips" :key="tip">{{ tip }}</li>
                  </ul>
                </div>
              </div>

              <!-- Video Recommendations -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.videoRecommendations') }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div v-for="video in videoRecommendations" :key="video.id" class="bg-gray-100 rounded-lg p-4">
                    <div class="aspect-video bg-gray-200 rounded-lg mb-3 flex items-center justify-center">
                      <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ video.title }}</h4>
                    <p class="text-sm text-gray-600">{{ video.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
              <!-- Other Games -->
              <div class="bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $t('gameDetail.otherGames') }}</h2>
                <div class="space-y-4">
                  <div 
                    v-for="otherGame in otherGames" 
                    :key="otherGame.id"
                    class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                    @click="$router.push(`/game/${otherGame.id}`)"
                  >
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <component :is="getGameIcon(otherGame.id)" class="w-6 h-6 text-gray-600" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">{{ otherGame.name }}</h4>
                      <p class="text-sm text-gray-600">{{ otherGame.players }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import ChessGame from '../games/chess/ChessGame.vue'
import GomokuGame from '../games/gomoku/GomokuGame.vue'
import CheckersGame from '../games/checkers/CheckersGame.vue'
import ReversiGame from '../games/reversi/ReversiGame.vue'

const route = useRoute()
const { t } = useI18n()

const props = defineProps<{
  gameId: string
}>()

const gameStarted = ref(false)

// Game data
const gameData = computed(() => {
  const games = {
    chess: {
      id: 'chess',
      name: t('games.chess.name'),
      description: t('games.chess.description'),
      history: t('games.chess.history'),
      difficulty: t('games.chess.difficulty'),
      players: t('games.chess.players')
    },
    checkers: {
      id: 'checkers',
      name: t('games.checkers.name'),
      description: t('games.checkers.description'),
      history: t('games.checkers.history'),
      difficulty: t('games.checkers.difficulty'),
      players: t('games.checkers.players')
    },
    reversi: {
      id: 'reversi',
      name: t('games.reversi.name'),
      description: t('games.reversi.description'),
      history: t('games.reversi.history'),
      difficulty: t('games.reversi.difficulty'),
      players: t('games.reversi.players')
    },
    gomoku: {
      id: 'gomoku',
      name: t('games.gomoku.name'),
      description: t('games.gomoku.description'),
      history: t('games.gomoku.history'),
      difficulty: t('games.gomoku.difficulty'),
      players: t('games.gomoku.players')
    }
  }
  
  return games[props.gameId as keyof typeof games] || null
})

// Game tags
const gameTags = computed(() => {
  const tags = {
    chess: ['Strategy', 'Classic', 'Competitive', 'Mind Game'],
    checkers: ['Strategy', 'Classic', 'Easy to Learn', 'Family Game'],
    reversi: ['Strategy', 'Logic', 'Quick Game', 'Tactical'],
    gomoku: ['Strategy', 'Pattern Recognition', 'Ancient', 'Simple Rules']
  }
  
  return tags[props.gameId as keyof typeof tags] || []
})

// Game tips
const gameTips = computed(() => {
  const tips = {
    chess: [
      'Control the center of the board',
      'Develop your pieces early',
      'Keep your king safe',
      'Think several moves ahead'
    ],
    checkers: [
      'Try to get your pieces to the back row',
      'Block your opponent\'s moves',
      'Force jumps when possible',
      'Keep your pieces together'
    ],
    reversi: [
      'Control the corners',
      'Limit your opponent\'s moves',
      'Think about mobility',
      'Plan for the endgame'
    ],
    gomoku: [
      'Block your opponent\'s four in a row',
      'Create multiple threats',
      'Control the center',
      'Look for fork opportunities'
    ]
  }
  
  return tips[props.gameId as keyof typeof tips] || []
})

// Video recommendations (placeholder)
const videoRecommendations = computed(() => [
  {
    id: 1,
    title: `How to Play ${gameData.value?.name}`,
    description: 'Learn the basic rules and strategies'
  },
  {
    id: 2,
    title: `Advanced ${gameData.value?.name} Tactics`,
    description: 'Master advanced techniques and strategies'
  }
])

// Other games
const otherGames = computed(() => {
  const allGames = [
    { id: 'chess', name: t('games.chess.name'), players: t('games.chess.players') },
    { id: 'checkers', name: t('games.checkers.name'), players: t('games.checkers.players') },
    { id: 'reversi', name: t('games.reversi.name'), players: t('games.reversi.players') },
    { id: 'gomoku', name: t('games.gomoku.name'), players: t('games.gomoku.players') }
  ]
  
  return allGames.filter(game => game.id !== props.gameId)
})

// Game icons
const gameIcon = computed(() => getGameIcon(props.gameId))

const getGameIcon = (gameId: string) => {
  const icons = {
    chess: 'ChessIcon',
    checkers: 'CheckersIcon',
    reversi: 'ReversiIcon',
    gomoku: 'GomokuIcon'
  }

  return icons[gameId as keyof typeof icons] || 'DefaultGameIcon'
}

// Game component
const gameComponent = computed(() => {
  const components = {
    chess: ChessGame,
    checkers: CheckersGame,
    reversi: ReversiGame,
    gomoku: GomokuGame
  }

  return components[props.gameId as keyof typeof components]
})

// Start game function
const startGame = () => {
  if (gameComponent.value) {
    gameStarted.value = true
  } else {
    alert(`${gameData.value?.name} game is coming soon!`)
  }
}

// Icon components (same as in GameCard.vue)
const ChessIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 22H5v-2h14v2M17.5 2.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5M12 6.5c-1.38 0-2.5 1.12-2.5 2.5s1.12 2.5 2.5 2.5 2.5-1.12 2.5-2.5-1.12-2.5-2.5-2.5M6.5 2.5C5.67 2.5 5 3.17 5 4s.67 1.5 1.5 1.5S8 4.83 8 4s-.67-1.5-1.5-1.5M12 12l-2 8h4l-2-8z"/>
    </svg>
  `
}

const CheckersIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>
  `
}

const ReversiIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
      <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6"/>
    </svg>
  `
}

const GomokuIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    </svg>
  `
}

const DefaultGameIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>
  `
}

onMounted(() => {
  // Update page title
  if (gameData.value) {
    document.title = `${gameData.value.name} - Poy8 Card`
  }
})
</script>

<style scoped>
.prose h3 {
  @apply text-lg font-semibold text-gray-900 mt-6 mb-3;
}

.prose p {
  @apply text-gray-600 mb-4;
}

.prose ul {
  @apply list-disc list-inside text-gray-600 space-y-1;
}
</style>
