<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div class="max-w-4xl mx-auto text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          {{ $t('about.title') }}
        </h1>
        <p class="text-xl text-gray-600 leading-relaxed">
          {{ $t('about.description') }}
        </p>
      </div>
    </section>

    <!-- Article Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-6">
            {{ $t('about.article.title') }}
          </h2>
          <p class="text-lg text-gray-600 leading-relaxed">
            {{ $t('about.article.intro') }}
          </p>
        </div>

        <div class="space-y-12">
          <!-- Chess Section -->
          <article class="bg-white rounded-xl p-8 shadow-sm">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              {{ $t('about.article.chessSection.title') }}
            </h3>
            <p class="text-gray-600 leading-relaxed">
              {{ $t('about.article.chessSection.content') }}
            </p>
          </article>

          <!-- Checkers Section -->
          <article class="bg-white rounded-xl p-8 shadow-sm">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              {{ $t('about.article.checkersSection.title') }}
            </h3>
            <p class="text-gray-600 leading-relaxed">
              {{ $t('about.article.checkersSection.content') }}
            </p>
          </article>

          <!-- Reversi Section -->
          <article class="bg-white rounded-xl p-8 shadow-sm">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              {{ $t('about.article.reversiSection.title') }}
            </h3>
            <p class="text-gray-600 leading-relaxed">
              {{ $t('about.article.reversiSection.content') }}
            </p>
          </article>

          <!-- Gomoku Section -->
          <article class="bg-white rounded-xl p-8 shadow-sm">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              {{ $t('about.article.gomokuSection.title') }}
            </h3>
            <p class="text-gray-600 leading-relaxed">
              {{ $t('about.article.gomokuSection.content') }}
            </p>
          </article>

          <!-- Brain Benefits Section -->
          <article class="bg-blue-50 rounded-xl p-8 shadow-sm border-l-4 border-blue-500">
            <h3 class="text-2xl font-bold text-blue-900 mb-4">
              {{ $t('about.article.brainBenefits.title') }}
            </h3>
            <p class="text-blue-800 leading-relaxed">
              {{ $t('about.article.brainBenefits.content') }}
            </p>
          </article>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            {{ $t('about.features.title') }}
          </h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                {{ $t('about.features.offline') }}
              </h3>
              <p class="text-gray-600">
                {{ $t('about.features.offlineDesc') }}
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                {{ $t('about.features.free') }}
              </h3>
              <p class="text-gray-600">
                {{ $t('about.features.freeDesc') }}
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                {{ $t('about.features.responsive') }}
              </h3>
              <p class="text-gray-600">
                {{ $t('about.features.responsiveDesc') }}
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                {{ $t('about.features.multilingual') }}
              </h3>
              <p class="text-gray-600">
                {{ $t('about.features.multilingualDesc') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Games Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Available Games</h2>
          <p class="text-xl text-gray-600">Choose from our collection of classic board games</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="game in games" :key="game.id" class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ game.name }}</h3>
            <p class="text-gray-600 text-sm mb-4">{{ game.description }}</p>
            <router-link 
              :to="`/game/${game.id}`"
              class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm"
            >
              {{ $t('home.playNow') }}
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Ready to Play?</h2>
        <p class="text-xl text-gray-600 mb-8">Start playing your favorite board games right now!</p>
        <router-link 
          to="/"
          class="btn btn-primary text-lg px-8 py-3"
        >
          {{ $t('home.playNow') }}
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const games = computed(() => [
  {
    id: 'chess',
    name: t('games.chess.name'),
    description: t('games.chess.description')
  },
  {
    id: 'checkers',
    name: t('games.checkers.name'),
    description: t('games.checkers.description')
  },
  {
    id: 'reversi',
    name: t('games.reversi.name'),
    description: t('games.reversi.description')
  },
  {
    id: 'gomoku',
    name: t('games.gomoku.name'),
    description: t('games.gomoku.description')
  }
])
</script>
