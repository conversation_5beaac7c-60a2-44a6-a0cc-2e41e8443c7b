<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
          {{ $t('home.title') }}
        </h1>
        <h2 class="text-2xl md:text-3xl font-semibold text-blue-600 mb-4">
          {{ $t('home.subtitle') }}
        </h2>
        <p class="text-lg md:text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          {{ $t('home.description') }}
        </p>
      </div>
    </section>

    <!-- Games Grid -->
    <section class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <GameCard
            v-for="game in games"
            :key="game.id"
            :game="game"
            @play="navigateToGame"
          />
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            {{ $t('about.features.title') }}
          </h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              {{ $t('about.features.offline') }}
            </h3>
            <p class="text-gray-600">
              {{ $t('about.features.offlineDesc') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              {{ $t('about.features.free') }}
            </h3>
            <p class="text-gray-600">
              {{ $t('about.features.freeDesc') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              {{ $t('about.features.responsive') }}
            </h3>
            <p class="text-gray-600">
              {{ $t('about.features.responsiveDesc') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              {{ $t('about.features.multilingual') }}
            </h3>
            <p class="text-gray-600">
              {{ $t('about.features.multilingualDesc') }}
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import GameCard from '../components/GameCard.vue'

const router = useRouter()
const { t } = useI18n()

const games = computed(() => [
  {
    id: 'match3',
    name: t('games.match3.name'),
    description: t('games.match3.description'),
    image: '/images/match3-preview.jpg',
    difficulty: t('games.match3.difficulty'),
    players: t('games.match3.players')
  },
  {
    id: 'bubble',
    name: t('games.bubble.name'),
    description: t('games.bubble.description'),
    image: '/images/bubble-preview.jpg',
    difficulty: t('games.bubble.difficulty'),
    players: t('games.bubble.players')
  },
  {
    id: 'tetris',
    name: t('games.tetris.name'),
    description: t('games.tetris.description'),
    image: '/images/tetris-preview.jpg',
    difficulty: t('games.tetris.difficulty'),
    players: t('games.tetris.players')
  },
  {
    id: 'connect',
    name: t('games.connect.name'),
    description: t('games.connect.description'),
    image: '/images/connect-preview.jpg',
    difficulty: t('games.connect.difficulty'),
    players: t('games.connect.players')
  },
  {
    id: 'jewels',
    name: t('games.jewels.name'),
    description: t('games.jewels.description'),
    image: '/images/jewels-preview.jpg',
    difficulty: t('games.jewels.difficulty'),
    players: t('games.jewels.players')
  },
  {
    id: 'zuma',
    name: t('games.zuma.name'),
    description: t('games.zuma.description'),
    image: '/images/zuma-preview.jpg',
    difficulty: t('games.zuma.difficulty'),
    players: t('games.zuma.players')
  },
  {
    id: 'mahjong',
    name: t('games.mahjong.name'),
    description: t('games.mahjong.description'),
    image: '/images/mahjong-preview.jpg',
    difficulty: t('games.mahjong.difficulty'),
    players: t('games.mahjong.players')
  },
  {
    id: 'collapse',
    name: t('games.collapse.name'),
    description: t('games.collapse.description'),
    image: '/images/collapse-preview.jpg',
    difficulty: t('games.collapse.difficulty'),
    players: t('games.collapse.players')
  }
])

const navigateToGame = (gameId: string) => {
  router.push(`/game/${gameId}`)
}
</script>
