import { createI18n } from 'vue-i18n'
import en from './en.json'
import zh from './zh.json'
import ja from './ja.json'
import ko from './ko.json'
import fr from './fr.json'
import es from './es.json'
import pt from './pt.json'
import it from './it.json'
import de from './de.json'
import la from './la.json'

const messages = {
  en,
  zh,
  ja,
  ko,
  fr,
  es,
  pt,
  it,
  de,
  la
}

export const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages
})

export const supportedLocales = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'es', name: 'Espa<PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'pt', name: '<PERSON>uguê<PERSON>', flag: '🇵🇹' },
  { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'la', name: 'Latina', flag: '🏛️' }
]
