import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface GameStats {
  gamesPlayed: number
  gamesWon: number
  gamesLost: number
  gamesDraw: number
  totalPlayTime: number // in seconds
}

interface GameSave {
  gameId: string
  gameState: any
  timestamp: number
  playerNames?: string[]
}

export const useGameStore = defineStore('game', () => {
  // State
  const stats = ref<Record<string, GameStats>>({})
  const savedGames = ref<GameSave[]>([])
  const preferences = ref({
    soundEnabled: true,
    animationsEnabled: true,
    autoSave: true,
    theme: 'light'
  })

  // Computed
  const totalGamesPlayed = computed(() => {
    return Object.values(stats.value).reduce((total, stat) => total + stat.gamesPlayed, 0)
  })

  const favoriteGame = computed(() => {
    let maxPlayed = 0
    let favorite = ''
    
    Object.entries(stats.value).forEach(([gameId, stat]) => {
      if (stat.gamesPlayed > maxPlayed) {
        maxPlayed = stat.gamesPlayed
        favorite = gameId
      }
    })
    
    return favorite
  })

  // Actions
  const getGameStats = (gameId: string): GameStats => {
    if (!stats.value[gameId]) {
      stats.value[gameId] = {
        gamesPlayed: 0,
        gamesWon: 0,
        gamesLost: 0,
        gamesDraw: 0,
        totalPlayTime: 0
      }
    }
    return stats.value[gameId]
  }

  const updateGameStats = (gameId: string, result: 'win' | 'loss' | 'draw', playTime: number) => {
    const gameStats = getGameStats(gameId)
    
    gameStats.gamesPlayed++
    gameStats.totalPlayTime += playTime
    
    switch (result) {
      case 'win':
        gameStats.gamesWon++
        break
      case 'loss':
        gameStats.gamesLost++
        break
      case 'draw':
        gameStats.gamesDraw++
        break
    }
    
    saveToLocalStorage()
  }

  const saveGame = (gameId: string, gameState: any, playerNames?: string[]) => {
    const existingIndex = savedGames.value.findIndex(save => save.gameId === gameId)
    const gameSave: GameSave = {
      gameId,
      gameState,
      timestamp: Date.now(),
      playerNames
    }
    
    if (existingIndex >= 0) {
      savedGames.value[existingIndex] = gameSave
    } else {
      savedGames.value.push(gameSave)
    }
    
    // Keep only the 10 most recent saves
    if (savedGames.value.length > 10) {
      savedGames.value.sort((a, b) => b.timestamp - a.timestamp)
      savedGames.value = savedGames.value.slice(0, 10)
    }
    
    saveToLocalStorage()
  }

  const loadGame = (gameId: string): GameSave | null => {
    return savedGames.value.find(save => save.gameId === gameId) || null
  }

  const deleteSavedGame = (gameId: string) => {
    const index = savedGames.value.findIndex(save => save.gameId === gameId)
    if (index >= 0) {
      savedGames.value.splice(index, 1)
      saveToLocalStorage()
    }
  }

  const updatePreferences = (newPreferences: Partial<typeof preferences.value>) => {
    preferences.value = { ...preferences.value, ...newPreferences }
    saveToLocalStorage()
  }

  const saveToLocalStorage = () => {
    try {
      localStorage.setItem('poy8-game-stats', JSON.stringify(stats.value))
      localStorage.setItem('poy8-saved-games', JSON.stringify(savedGames.value))
      localStorage.setItem('poy8-preferences', JSON.stringify(preferences.value))
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  const loadFromLocalStorage = () => {
    try {
      const savedStats = localStorage.getItem('poy8-game-stats')
      if (savedStats) {
        stats.value = JSON.parse(savedStats)
      }

      const savedGamesData = localStorage.getItem('poy8-saved-games')
      if (savedGamesData) {
        savedGames.value = JSON.parse(savedGamesData)
      }

      const savedPreferences = localStorage.getItem('poy8-preferences')
      if (savedPreferences) {
        preferences.value = { ...preferences.value, ...JSON.parse(savedPreferences) }
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error)
    }
  }

  const clearAllData = () => {
    stats.value = {}
    savedGames.value = []
    preferences.value = {
      soundEnabled: true,
      animationsEnabled: true,
      autoSave: true,
      theme: 'light'
    }
    
    try {
      localStorage.removeItem('poy8-game-stats')
      localStorage.removeItem('poy8-saved-games')
      localStorage.removeItem('poy8-preferences')
    } catch (error) {
      console.warn('Failed to clear localStorage:', error)
    }
  }

  const exportData = () => {
    return {
      stats: stats.value,
      savedGames: savedGames.value,
      preferences: preferences.value,
      exportDate: new Date().toISOString()
    }
  }

  const importData = (data: any) => {
    try {
      if (data.stats) stats.value = data.stats
      if (data.savedGames) savedGames.value = data.savedGames
      if (data.preferences) preferences.value = { ...preferences.value, ...data.preferences }
      
      saveToLocalStorage()
      return true
    } catch (error) {
      console.error('Failed to import data:', error)
      return false
    }
  }

  // Initialize
  loadFromLocalStorage()

  return {
    // State
    stats,
    savedGames,
    preferences,
    
    // Computed
    totalGamesPlayed,
    favoriteGame,
    
    // Actions
    getGameStats,
    updateGameStats,
    saveGame,
    loadGame,
    deleteSavedGame,
    updatePreferences,
    saveToLocalStorage,
    loadFromLocalStorage,
    clearAllData,
    exportData,
    importData
  }
})
