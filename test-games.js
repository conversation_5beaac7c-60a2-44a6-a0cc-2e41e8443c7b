// Simple test script to verify game functionality
// Run this in browser console on each game page

const testGameFunctionality = (gameName) => {
  console.log(`Testing ${gameName} game...`);
  
  // Check if game component is loaded
  const gameElement = document.querySelector('.chess-game, .checkers-game, .reversi-game, .gomoku-game');
  if (!gameElement) {
    console.error(`❌ ${gameName} game component not found`);
    return false;
  }
  
  console.log(`✅ ${gameName} game component loaded`);
  
  // Check if board is rendered
  const boardElement = document.querySelector('.chess-board, .checkers-board, .reversi-board, .gomoku-board');
  if (!boardElement) {
    console.error(`❌ ${gameName} board not found`);
    return false;
  }
  
  console.log(`✅ ${gameName} board rendered`);
  
  // Check if game controls are present
  const resetButton = document.querySelector('button');
  if (!resetButton) {
    console.error(`❌ ${gameName} controls not found`);
    return false;
  }
  
  console.log(`✅ ${gameName} controls present`);
  
  // Check if clickable areas exist
  const clickableAreas = document.querySelectorAll('.chess-cell, .checkers-cell, .reversi-cell, .click-area');
  if (clickableAreas.length === 0) {
    console.error(`❌ ${gameName} clickable areas not found`);
    return false;
  }
  
  console.log(`✅ ${gameName} has ${clickableAreas.length} clickable areas`);
  
  console.log(`🎉 ${gameName} game test passed!`);
  return true;
};

// Test all games
const testAllGames = async () => {
  const games = ['chess', 'checkers', 'reversi', 'gomoku'];
  const results = {};
  
  for (const game of games) {
    console.log(`\n--- Testing ${game} ---`);
    
    try {
      // Navigate to game page
      window.location.href = `/game/${game}`;
      
      // Wait for page load
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Test game functionality
      results[game] = testGameFunctionality(game);
      
    } catch (error) {
      console.error(`❌ Error testing ${game}:`, error);
      results[game] = false;
    }
  }
  
  // Summary
  console.log('\n=== TEST SUMMARY ===');
  Object.entries(results).forEach(([game, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${game}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  console.log(`\nTotal: ${passedCount}/${games.length} games passed`);
  
  return results;
};

// Manual test functions for individual games
const testChess = () => {
  console.log('Testing Chess game manually...');
  
  // Try to click on a pawn
  const pawnSquare = document.querySelector('.chess-cell:nth-child(14)'); // e2 square
  if (pawnSquare) {
    pawnSquare.click();
    console.log('✅ Clicked on pawn square');
    
    // Check if possible moves are highlighted
    setTimeout(() => {
      const possibleMoves = document.querySelectorAll('.possible-move');
      console.log(`✅ Found ${possibleMoves.length} possible moves`);
    }, 100);
  }
};

const testCheckers = () => {
  console.log('Testing Checkers game manually...');
  
  // Try to click on a red piece
  const redPiece = document.querySelector('.checkers-piece.red');
  if (redPiece) {
    redPiece.parentElement.click();
    console.log('✅ Clicked on red piece');
    
    // Check if possible moves are highlighted
    setTimeout(() => {
      const possibleMoves = document.querySelectorAll('.possible-move');
      console.log(`✅ Found ${possibleMoves.length} possible moves`);
    }, 100);
  }
};

const testReversi = () => {
  console.log('Testing Reversi game manually...');
  
  // Try to click on a possible move
  const possibleMove = document.querySelector('.possible-move');
  if (possibleMove) {
    possibleMove.click();
    console.log('✅ Clicked on possible move');
    
    // Check if pieces were placed/flipped
    setTimeout(() => {
      const blackPieces = document.querySelectorAll('.reversi-piece.black');
      const whitePieces = document.querySelectorAll('.reversi-piece.white');
      console.log(`✅ Board has ${blackPieces.length} black and ${whitePieces.length} white pieces`);
    }, 100);
  }
};

const testGomoku = () => {
  console.log('Testing Gomoku game manually...');
  
  // Try to click on center of board
  const centerArea = document.querySelector('.click-area:nth-child(113)'); // Approximate center
  if (centerArea) {
    centerArea.click();
    console.log('✅ Clicked on center area');
    
    // Check if piece was placed
    setTimeout(() => {
      const pieces = document.querySelectorAll('.piece');
      console.log(`✅ Board has ${pieces.length} pieces`);
    }, 100);
  }
};

// Export functions for manual testing
window.testGameFunctionality = testGameFunctionality;
window.testAllGames = testAllGames;
window.testChess = testChess;
window.testCheckers = testCheckers;
window.testReversi = testReversi;
window.testGomoku = testGomoku;

console.log('Game testing functions loaded!');
console.log('Available functions:');
console.log('- testGameFunctionality(gameName)');
console.log('- testAllGames()');
console.log('- testChess()');
console.log('- testCheckers()');
console.log('- testReversi()');
console.log('- testGomoku()');
