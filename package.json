{"name": "poy8-eliminating-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc --noEmit && vite build", "build:cf": "npm install --no-audit --no-fund && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"pinia": "^2.1.7", "vue": "^3.4.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.8.3", "vite": "^5.0.8", "vite-plugin-pwa": "^0.17.4", "vue-tsc": "^2.0.0"}}